package com.hainancrc.module.codeengine.entity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 渠道 DO
 *
 * <AUTHOR>
 */
@TableName("codeengine_channel")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelDO extends BaseDO {

    /**
     * 渠道ID
     */
    @TableId
    private Long id;
    /**
     * 渠道名称
     */
    private String channelName;
    /**
     * 渠道code
     */
    private String channelCode;
    /**
     * 渠道描述
     */
    private String channelDescription;

    /**
     * 提交人
     */
    private String presenter;

}
