package com.hainancrc.module.codeengine.api.scene.dto;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
 * 场景
 */
@Data
public class SceneBaseDTO {

    @ApiModelProperty(value = "场景名称", required = true)
    @NotEmpty(message = "场景名称不能为空")
    private String sceneName;

    @ApiModelProperty(value = "场景描述", required = true)
    @NotEmpty(message = "场景描述不能为空")
    private String sceneDescription;

    @ApiModelProperty(value = "场景码Logo", required = false)
    private String sceneCodeLogo;

    @ApiModelProperty(value = "申码授权ID")
    private String applyAuthId;

    @ApiModelProperty(value = "亮码授权ID")
    private String generateAuthId;

    @ApiModelProperty(value = "解码授权ID")
    private String decodeAuthId;

    @ApiModelProperty(value = "规则名称", required = true)
    @NotEmpty(message = "规则名称不能为空")
    private String ruleName;

    @ApiModelProperty(value = "静态规则过期月数(null为永久有效)")
    private Integer staticExpiredMonth;

    @ApiModelProperty(value = "动态规则过期次数", required = false)
    private Integer dynamicExpiredTimes;

    @ApiModelProperty(value = "动态规则过期分钟数", required = false)
    private Integer dynamicExpiredMinute;

    @ApiModelProperty(value = "场景是否启用", required = true)
    private Boolean sceneEnable;

    @ApiModelProperty(value = "提交人")
    private String presenter;

    @ApiModelProperty(value = "额外参数")
    private String extraParameters;

}
