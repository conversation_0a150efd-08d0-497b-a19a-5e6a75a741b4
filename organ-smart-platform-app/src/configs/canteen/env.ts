console.log(`当前的webpack环境变量为: ${process.env.NODE_ENV}`);
console.log(`当前的webpack环境变量为: ${process.env.VUE_APP_ENV}`);

export const envConfig: Record<
  string,
  {
    /** 登录地址 */
    externalLogin: string;
    /** 退出登录地址 */
    externalLogout: string;
  }
> = {
  /** 开发环境 */
  DEV: {
    externalLogin: "https://uaa-dev.digitalhainan.com.cn/login",
    externalLogout: "https://uaa-dev.digitalhainan.com.cn/logout",
  },
  /** 测试环境 */
  SIT: {
    externalLogin: "https://uaa-dev.digitalhainan.com.cn/login",
    externalLogout: "https://uaa-dev.digitalhainan.com.cn/logout",
  },
  /** 生产环境 */
  PROD: {
    externalLogin: "https://uaa.digitalhainan.com.cn/login",
    externalLogout: "https://uaa.digitalhainan.com.cn/logout",
  },
};

/** 当前的环境配置 */
export const env = envConfig[process.env.VUE_APP_ENV];
