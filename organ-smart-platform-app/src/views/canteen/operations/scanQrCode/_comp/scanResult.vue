<template>
  <div v-if="result" class="scan-result" @click.stop>
    <!-- 扫码结果 -->
    <div class="result-content success">
      <!-- 顶部 渐变背景+图片 -->
      <div class="resule-header">
        <img
          v-if="!result.success"
          src="@/assets/images/canteen/failed.png"
          class="result-img"
        />
        <img
          v-else
          src="@/assets/images/canteen/success.png"
          class="result-img"
        />
      </div>

      <!-- 扫码结果文本 -->
      <div class="result-text">
        {{ result.success ? "核销成功" : "核销失败" }}
      </div>

      <div class="content-pd">
        <!-- 扫码信息卡片 -->
        <div
          :class="['booking-info-card', !result.success ? 'failed' : 'success']"
        >
          <div v-if="!result.success" class="failed-card">
            <div>{{ result.errorMessage }}</div>
          </div>

          <div v-else class="success-card">
            <div class="info-item">
              <span class="label">用户姓名</span>
              <span class="value">{{ result.bookingInfo.username }}</span>
            </div>
            <div class="info-item">
              <span class="label">餐次类型</span>
              <span class="value">{{ result.bookingInfo.mealName }}</span>
            </div>
            <div class="info-item">
              <span class="label">使用时间</span>
              <span class="value">{{
                result.bookingInfo.checkinTime
                  ? dayjs(result.bookingInfo.checkinTime).format(
                      "YYYY/MM/DD HH:mm:ss"
                    )
                  : "-"
              }}</span>
            </div>
          </div>
        </div>

        <div class="card-footer">
          <!-- 操作按钮 -->
          <div class="result-actions">
            <button
              v-if="!result.success"
              class="canteen-btn btn-primary action-btn"
              @click="handleRescanQRCode"
            >
              重新扫码
            </button>
            <button
              v-else
              class="canteen-btn btn-primary action-btn"
              @click="handleContinueScan"
            >
              继续扫码
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { IConsumeResult } from "@/api/canteen/operations/types/consume";

/**
 * 扫码结果弹窗组件Props接口
 */
interface IProps {
  /** 扫码结果数据 */
  result: IConsumeResult;
}

/**
 * 组件事件接口
 */
interface IEmits {
  (e: "rescan", payload?: string): void;
  (e: "continue-scan"): void;
}

const props = withDefaults(defineProps<IProps>(), {});
const emit = defineEmits<IEmits>();

/**
 * 处理继续扫码按钮点击事件
 */
const handleContinueScan = () => {
  emit("continue-scan");
};

/**
 * 处理重新扫码按钮点击事件
 */
const handleRescanQRCode = () => {
  emit("rescan");
};
</script>

<style scoped lang="scss">
.scan-result {
  background: #ffffff;
  border-radius: 10px;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .content-pd {
    padding: 20px;
  }

  .result-content {
    padding: 0 0 24px;
    text-align: center;
    position: relative;
    border-radius: 12px;

    .resule-header {
      height: 100px;
      padding-top: 12px;
      background: linear-gradient(
        180deg,
        rgba(177, 204, 246, 1) 0%,
        rgba(255, 255, 255, 0) 100%
      );

      .result-img {
        width: auto;
        height: 100%;
        object-fit: cover;
      }
    }

    .result-text {
      color: #383838;
      font-size: 20px;
      font-weight: 500;
      margin-top: 6px;
    }

    // 预约信息卡片
    .booking-info-card {
      border-radius: 4px;
      padding: 20px;
      text-align: left;
      // backdrop-filter: blur(10px);

      &.success {
        background-color: #f0f8ff;
      }

      &.failed {
        background-color: #fff5f5;
        color: #ff4d4f;
      }

      .card-header {
        margin-bottom: 16px;

        .card-title {
          font-size: 18px;
          font-weight: 600;
          color: #383838;
          margin: 0;
        }
      }

      .info-item {
        display: flex;
        align-items: center;
        padding: 6px 0;

        .label {
          flex-shrink: 0;
          width: 80px;
          font-size: 16px;
          color: #808080;
        }

        .value {
          flex-grow: 1;
          font-size: 16px;
          color: #1d1e20;
        }
      }

      .failed-card {
        text-align: center;
        padding: 50px 0;
      }
    }

    .card-footer {
      padding-top: 30px;
      width: 100%;
      margin-bottom: 50px;
    }

    // 操作按钮
    .result-actions {
      width: 100%;
      display: flex;
      justify-content: center;
      gap: 12px;

      .action-btn {
        flex: 1;
        max-width: 86%;
        padding: 16px;
        font-size: 18px;
      }
    }
  }
}
</style>
