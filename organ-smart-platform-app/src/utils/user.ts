import { env } from "@/configs/env";

/**
 * 获取cookie
 */
export function getCookie(cname) {
  let name = cname + "=";
  let ca = document.cookie.split(";");
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i].trim();
    if (c.indexOf(name) == 0) {
      return c.substring(name.length, c.length);
    }
  }
}

/**
 * 获取登录url
 */
export function getLoginUrl(moduleName: string = "canteen") {
  const { externalLogin } = env[moduleName];
  if (externalLogin) {
    return externalLogin;
  }
  return "";
}

/**
 * 跳转至登录页
 */
export function toLogin() {
  window.location.href = getLoginUrl();
}
