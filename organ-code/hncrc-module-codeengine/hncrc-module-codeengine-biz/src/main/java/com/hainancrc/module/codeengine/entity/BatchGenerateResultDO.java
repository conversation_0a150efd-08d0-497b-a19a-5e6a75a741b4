package com.hainancrc.module.codeengine.entity;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 批量生成结果 DO
 *
 * <AUTHOR>
 */
@TableName("codeengine_batch_generate_result")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchGenerateResultDO extends BaseDO {

    /**
     * 批量生成结果ID
     */
    @TableId
    private Long id;
    /**
     * 生成订单号
     */
    private String generateOrderId;
    /**
     * 二维码url
     */
    private String codeUrl;

    /**
     * 场景码二维码LogoUrl
     */
    private String sceneCodeLogo;

}
