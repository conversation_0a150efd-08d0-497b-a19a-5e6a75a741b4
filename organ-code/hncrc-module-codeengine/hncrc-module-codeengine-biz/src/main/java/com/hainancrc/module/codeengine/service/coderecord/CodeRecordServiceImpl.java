package com.hainancrc.module.codeengine.service.coderecord;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.APPLICATION_CHANNEL_REL_NOT_EXISTS;
import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.APPLICATION_NOT_EXISTS;
import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.APPLICATION_SCENE_REL_NOT_EXISTS;
import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.APPLY_NOT_EXISTS;
import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.BATCH_GENERATE_NOT_EXISTS;
import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.CHANNEL_NOT_EXISTS;
import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.CODE_BATCH_CREATE_ERROR;
import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.CODE_RECORD_NOT_EXISTS;
import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.IMPORT_EXCEL_ERROR;
import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.SCENE_EXPIRED_TIME_ERROR;
import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.SCENE_NOT_EXISTS;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.validation.Valid;

import cn.hutool.core.util.StrUtil;
import com.hainancrc.module.codeengine.api.coderecord.RequireCodeInfoCreateDTO;
import com.hainancrc.module.codeengine.api.coderecord.vo.CodeRecordGeneratorCodeInfoRespVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageConfig;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.codeengine.api.coderecord.CodeRecordBatchDTO;
import com.hainancrc.module.codeengine.api.coderecord.CodeRecordCreateDTO;
import com.hainancrc.module.codeengine.api.coderecord.vo.BatchCodeRecordGeneratorCodeRespVO;
import com.hainancrc.module.codeengine.api.coderecord.vo.CodeRecordGeneratorCodeRespVO;
import com.hainancrc.module.codeengine.entity.ApplicationDO;
import com.hainancrc.module.codeengine.entity.ApplyChannelRelDO;
import com.hainancrc.module.codeengine.entity.ApplyDO;
import com.hainancrc.module.codeengine.entity.BatchGenerateDO;
import com.hainancrc.module.codeengine.entity.BatchGenerateResultDO;
import com.hainancrc.module.codeengine.entity.ChannelDO;
import com.hainancrc.module.codeengine.entity.CodeLogDO;
import com.hainancrc.module.codeengine.entity.CodeRecordDO;
import com.hainancrc.module.codeengine.entity.SceneDO;
import com.hainancrc.module.codeengine.enums.ApplyCategory;
import com.hainancrc.module.codeengine.enums.CodeLogCategory;
import com.hainancrc.module.codeengine.enums.CodeLogStatus;
import com.hainancrc.module.codeengine.mapper.application.ApplicationMapper;
import com.hainancrc.module.codeengine.mapper.apply.ApplyMapper;
import com.hainancrc.module.codeengine.mapper.applychannelrel.ApplyChannelRelMapper;
import com.hainancrc.module.codeengine.mapper.batchgenerate.BatchGenerateMapper;
import com.hainancrc.module.codeengine.mapper.batchgenerateresult.BatchGenerateResultMapper;
import com.hainancrc.module.codeengine.mapper.channel.ChannelMapper;
import com.hainancrc.module.codeengine.mapper.codelog.CodeLogMapper;
import com.hainancrc.module.codeengine.mapper.coderecord.CodeRecordMapper;
import com.hainancrc.module.codeengine.mapper.scene.SceneMapper;
import com.hainancrc.module.codeengine.service.common.CodeService;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import lombok.extern.slf4j.Slf4j;

/**
 * 数据源 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CodeRecordServiceImpl implements CodeRecordService {

    @Resource
    private ApplicationMapper applicationMapper;

    @Resource
    private SceneMapper sceneMapper;

    @Resource
    private ChannelMapper channelMapper;

    @Resource
    private ApplyMapper applyMapper;

    @Resource
    private CodeService codeService;

    @Resource
    private CodeLogMapper codeLogMapper;

    @Resource
    private BatchGenerateMapper batchGenerateMapper;

    @Resource
    private BatchGenerateResultMapper batchGenerateResultMapper;

    @Resource
    private ApplyChannelRelMapper applyChannelRelMapper;

    @Resource
    private CodeRecordMapper codeRecordMapper;

    private ByteArrayOutputStream codeTextToImageFile(String code) throws IOException, WriterException {

        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        BitMatrix bitMatrix = qrCodeWriter.encode(code, BarcodeFormat.QR_CODE, 800, 800);
        ByteArrayOutputStream pngOutputStream = new ByteArrayOutputStream();
        MatrixToImageConfig config = new MatrixToImageConfig(0xFF000000, 0xFFFFFFFF);
        MatrixToImageWriter.writeToStream(bitMatrix, "PNG", pngOutputStream, config);

        return pngOutputStream;

    }

    private ByteArrayOutputStream codeTextToImageFile(String code, String codeLogoUrl)
            throws IOException, WriterException {
        QrConfig qrConfig = new QrConfig();
        try {
            qrConfig
                    .setHeight(800)
                    .setWidth(800)
                    .setImg(ImageIO.read(new URL(codeLogoUrl)));
        } catch (Exception ignored) {
            qrConfig = new QrConfig();
            qrConfig
                    .setHeight(800)
                    .setWidth(800);
        }
        ByteArrayOutputStream pngOutputStream = new ByteArrayOutputStream();
        pngOutputStream.write(QrCodeUtil.generatePng(code, qrConfig));
        return pngOutputStream;
    }

    private byte[] codeTextToCodeFile(List<BatchGenerateResultDO> batchGenerateResultDOs)
            throws WriterException, IOException {

        ArrayList<ByteArrayInputStream> listOfBuff = new ArrayList<>();
        ArrayList<String> fileNames = new ArrayList<>();
        int i = 0;
        for (BatchGenerateResultDO batchGenerateResultDO : batchGenerateResultDOs) {
            i++;
            ApplyDO applyDO = getApplyDO(batchGenerateResultDO.getCodeUrl());
            String entity = "";

            if (StringUtils.isNotEmpty(applyDO.getEntity()) &&
                    StringUtils.isNotEmpty(applyDO.getEntityId())) {
                entity = applyDO.getEntity() + "_" + applyDO.getEntityId() + "_";
            }
            String sceneCodeUrl = batchGenerateResultDO.getSceneCodeLogo();
            ByteArrayOutputStream pngOutputStream;
            if (sceneCodeUrl == null || StringUtils.isBlank(sceneCodeUrl)) {
                pngOutputStream = codeTextToImageFile(batchGenerateResultDO.getCodeUrl());
            } else {
                pngOutputStream = codeTextToImageFile(batchGenerateResultDO.getCodeUrl(), sceneCodeUrl);
            }
            listOfBuff.add(new ByteArrayInputStream(pngOutputStream.toByteArray()));
            fileNames.add(String.format(entity + "%06d.png", i));
        }
        ByteArrayOutputStream zipOutputStream = new ByteArrayOutputStream();
        ZipUtil.zip(zipOutputStream, fileNames.toArray(new String[0]), listOfBuff.toArray(new ByteArrayInputStream[0]));

        byte[] buff = zipOutputStream.toByteArray();
        return buff;

    }

    @Override
    public CodeRecordGeneratorCodeRespVO createCodeImage(CodeRecordCreateDTO createDTO) {
        CodeRecordBatchDTO dto = new CodeRecordBatchDTO();
        dto.setChannelIds(createDTO.getChannelIds());
        dto.setEntity(createDTO.getEntity());
        dto.setEntityId(createDTO.getEntityId());
        dto.setExpiredTime(createDTO.getExpiredTime());
        dto.setSceneId(createDTO.getSceneId());
        dto.setCodeCategory(ApplyCategory.STATIC);
        dto.setApplyDescription(createDTO.getApplyDescription());

        SceneDO sceneDO = sceneMapper.selectById(createDTO.getSceneId());
        if (sceneDO == null) {
            throw exception(SCENE_NOT_EXISTS);
        }
        String code = createOneCodeInternal(createDTO.getApplicationId(), dto);

        CodeRecordGeneratorCodeRespVO VO = new CodeRecordGeneratorCodeRespVO();
        String sceneCodeUrl = sceneDO.getSceneCodeLogo();
        VO.setCodeImageBase64(createQrCodeImageByteStr(sceneCodeUrl, code));
        return VO;

    }

    private String createQrCodeImageByteStr(String sceneCodeUrl, String code) {
        ByteArrayOutputStream stream;
        try {
            if (sceneCodeUrl == null || StringUtils.isBlank(sceneCodeUrl)) {
                stream = codeTextToImageFile(code);
            } else {
                stream = codeTextToImageFile(code, sceneCodeUrl);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw exception(CODE_BATCH_CREATE_ERROR);
        }
        byte[] buff = stream.toByteArray();
        return "data:image/png;base64," + java.util.Base64.getEncoder().encodeToString(buff);
    }

    @Override
    public BatchCodeRecordGeneratorCodeRespVO createCodeBatch(Long applicationId, byte[] fileBytes) {

        DataFormatter df = new DataFormatter();
        byte[] excelBuff = fileBytes;
        // excel列:场景,渠道(允许多个),到期时间(yyyy-MM-dd HH:mm:ss),主体ID,主体,描述
        ArrayList<CodeRecordBatchDTO> dtos = new ArrayList<>();

        // using poi read buff as excel file
        try (ByteArrayInputStream bis = new ByteArrayInputStream(excelBuff);
                Workbook workbook = WorkbookFactory.create(bis)) {

            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rows = sheet.rowIterator();

            rows.next(); // skip first row

            while (rows.hasNext()) {
                Row row = rows.next();
                Iterator<Cell> cellIterator = row.cellIterator();
                SceneDO sceneDO = null;
                CodeRecordBatchDTO dto = new CodeRecordBatchDTO();
                dto.setCodeCategory(ApplyCategory.STATIC);
                while (cellIterator.hasNext()) {
                    Cell cell = cellIterator.next();
                    int cellIndex = cell.getColumnIndex();
                    String cellStringValue = df.formatCellValue(cell).trim();
                    switch (cellIndex) {
                        case 0:
                            sceneDO = sceneMapper.selectByCode(cellStringValue);
                            if (sceneDO == null) {
                                throw exception(SCENE_NOT_EXISTS);
                            }
                            dto.setSceneId(sceneDO.getId());
                            dto.setSceneCodeLogo(sceneDO.getSceneCodeLogo());
                            break;
                        case 1:

                            String[] channelSpliter = cellStringValue.split(",");
                            ArrayList<Long> channelIds = new ArrayList<>();
                            for (String channelCode : channelSpliter) {
                                String channelTrimed = channelCode.trim();
                                if (channelTrimed.isEmpty())
                                    continue;

                                ChannelDO channelDO = channelMapper.selectByCode(channelTrimed);
                                if (channelDO == null) {
                                    throw exception(CHANNEL_NOT_EXISTS);
                                }
                                channelIds.add(channelDO.getId());
                            }
                            dto.setChannelIds(channelIds.toArray(new Long[channelIds.size()]));

                            break;
                        case 2:
                            if (!cellStringValue.trim().isEmpty()) {
                                dto.setExpiredTime(cell.getDateCellValue());
                                if (!codeService.validateExpired(sceneDO, ApplyCategory.STATIC,
                                        dto.getExpiredTime())) {
                                    throw exception(SCENE_EXPIRED_TIME_ERROR);
                                }
                            }
                            break;
                        case 3:
                            dto.setEntityId(cellStringValue);
                            break;
                        case 4:
                            dto.setEntity(cellStringValue);
                            break;
                        case 5:
                            dto.setApplyDescription(cellStringValue);
                            break;
                        default:
                            break;
                    }
                }
                dtos.add(dto);
            }

        } catch (IOException e) {
            throw exception(IMPORT_EXCEL_ERROR);
        }

        BatchGenerateDO batchGenerateDO = new BatchGenerateDO();
        batchGenerateDO.setApplicationId(applicationId);
        batchGenerateDO.setGenerateOrderId(UUID.randomUUID().toString());
        batchGenerateDO.setGenerateCount(dtos.size());
        batchGenerateMapper.insert(batchGenerateDO);

        for (CodeRecordBatchDTO dto : dtos) {
            BatchGenerateResultDO DO = new BatchGenerateResultDO();
            DO.setGenerateOrderId(batchGenerateDO.getGenerateOrderId());
            DO.setCodeUrl(createOneCodeInternal(applicationId, dto));
            DO.setSceneCodeLogo(dto.getSceneCodeLogo());
            batchGenerateResultMapper.insert(DO);
        }
        BatchCodeRecordGeneratorCodeRespVO VO = new BatchCodeRecordGeneratorCodeRespVO();
        VO.setGenerateOrderId(batchGenerateDO.getGenerateOrderId());
        return VO;
    }

    private String createOneCodeInternal(Long applicationId, CodeRecordBatchDTO dto) {
        ApplicationDO applicationDO = applicationMapper.selectById(applicationId);
        if (applicationDO == null) {
            throw exception(APPLICATION_NOT_EXISTS);
        }

        SceneDO sceneDO = sceneMapper.selectById(dto.getSceneId());
        if (sceneDO == null) {
            throw exception(SCENE_NOT_EXISTS);
        }
        if (dto.getExpiredTime() != null && !codeService.validateExpired(sceneDO, dto.getCodeCategory(),
                dto.getExpiredTime())) {
            throw exception(SCENE_EXPIRED_TIME_ERROR);
        }
        for (Long channelId : dto.getChannelIds()) {
            // 检查每个 channel 是否有跟 application 关联
            if (!codeService.applicationRelatedChannel(applicationId, channelId)) {
                throw exception(APPLICATION_CHANNEL_REL_NOT_EXISTS);
            }
        }
        // 检查每个 scene 是否有跟 application 关联
        if (!codeService.applicationRelatedScene(applicationId, dto.getSceneId())) {
            throw exception(APPLICATION_SCENE_REL_NOT_EXISTS);
        }

        //
        List<ChannelDO> channelDOS = channelMapper
                .selectList(new LambdaQueryWrapper<ChannelDO>().in(ChannelDO::getId, dto.getChannelIds()));
        String channelCodes = channelDOS.stream().map(ChannelDO::getChannelCode).distinct()
                .collect(Collectors.joining(","));
        ApplyDO applyDO = new ApplyDO();
        applyDO.setGeneratedCount(1); // 因为接下来马上就会进行亮码操作，所以这里设置为 1
        applyDO.setApplicationId(applicationId);
        applyDO.setSceneId(dto.getSceneId());
        applyDO.setEntityId(dto.getEntityId());
        applyDO.setEntity(dto.getEntity());
        applyDO.setExpiredTime(dto.getExpiredTime());
        applyDO.setApplyDescription(dto.getApplyDescription());
        applyMapper.insert(applyDO);
        for (Long channelId : dto.getChannelIds()) {
            ApplyChannelRelDO applyChannelRelDO = new ApplyChannelRelDO();
            applyChannelRelDO.setApplyId(applyDO.getId());
            applyChannelRelDO.setChannelId(channelId);
            applyChannelRelMapper.insert(applyChannelRelDO);
        }
        CodeRecordDO codeRecord = codeService.createCodeRecord(applyDO, applicationDO.getApplicationCodePrefix(),
                dto.getCodeCategory());
        // 亮码记录
        CodeLogDO codeLogDO = new CodeLogDO();
        codeLogDO.setCodeCategory(dto.getCodeCategory());
        codeLogDO.setChannelIdStr(codeService.comboChannelIdToStr(dto.getChannelIds()));
        codeLogDO.setChannelCode(channelCodes);
        codeLogDO.setCodeLogCategory(CodeLogCategory.GENERATE);
        codeLogDO.setCodeLogAuthId(null);
        codeLogDO.setCodeLogAuthOrderId(null);
        codeLogDO.setCodeLogApplyId(applyDO.getId());
        codeLogDO.setCodeKey(codeRecord.getCodeKey());
        codeLogDO.setLogStatus(CodeLogStatus.SUCCESS);
        codeLogDO.setApplicationId(applicationId);
        codeLogDO.setSceneId(dto.getSceneId());
        codeLogDO.setEntityId(dto.getEntityId());
        codeLogDO.setEntity(dto.getEntity());
        codeLogMapper.insert(codeLogDO);
        codeLogDO.setCodeLogOrderId(codeService.createCodeLogOrderId(codeLogDO.getId()));
        codeLogMapper.updateById(codeLogDO);
        return codeRecord.getCodeUrl();
    }

    @Override
    public byte[] getCodesByGenerateOrderId(String generateOrderId) {

        BatchGenerateDO batchGenerateDO = batchGenerateMapper.selectOne(new LambdaQueryWrapperX<BatchGenerateDO>()
                .eq(BatchGenerateDO::getGenerateOrderId, generateOrderId));

        if (batchGenerateDO == null) {
            throw exception(BATCH_GENERATE_NOT_EXISTS);
        }

        List<BatchGenerateResultDO> batchGenerateResultDOs = batchGenerateResultMapper
                .selectList(new LambdaQueryWrapperX<BatchGenerateResultDO>()
                        .eq(BatchGenerateResultDO::getGenerateOrderId, generateOrderId));

        byte[] buff = null;
        try {
            buff = codeTextToCodeFile(batchGenerateResultDOs);
        } catch (Exception e) {
            e.printStackTrace();
            throw exception(CODE_BATCH_CREATE_ERROR);
        }
        return buff;

    }

    @Override
    public String getDownloadNameFromGenerateOrderId(String generateOrderId) {

        BatchGenerateDO batchGenerateDO = batchGenerateMapper.selectOne(new LambdaQueryWrapperX<BatchGenerateDO>()
                .eq(BatchGenerateDO::getGenerateOrderId, generateOrderId));

        if (batchGenerateDO == null) {
            throw exception(BATCH_GENERATE_NOT_EXISTS);
        }

        List<BatchGenerateResultDO> batchGenerateResultDOs = batchGenerateResultMapper
                .selectList(new LambdaQueryWrapperX<BatchGenerateResultDO>()
                        .eq(BatchGenerateResultDO::getGenerateOrderId, generateOrderId));

        if (batchGenerateResultDOs.size() == 0) {
            throw exception(BATCH_GENERATE_NOT_EXISTS);
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss_SSS");
        String formattedDateTime = LocalDateTime.now().format(formatter);
        String fileName = "code_" + formattedDateTime + ".zip";
        return fileName;

    }

    @Override
    @Transactional
    public CodeRecordGeneratorCodeInfoRespVO requireCreatingCodeInfo(RequireCodeInfoCreateDTO createDTO) {
        ApplicationDO applicationDO = applicationMapper.selectByCode(createDTO.getApplicationCode());
        if (applicationDO == null) {
            throw exception(APPLICATION_NOT_EXISTS);
        }
        SceneDO sceneDO = sceneMapper.selectByCode(createDTO.getSceneCode());
        if (sceneDO == null) {
            throw exception(SCENE_NOT_EXISTS);
        }
        if (createDTO.getExpiredTime() != null && !codeService.validateExpired(sceneDO, createDTO.getCodeCategory(), createDTO.getExpiredTime())) {
            throw exception(SCENE_EXPIRED_TIME_ERROR);
        }
        // 检查每个 scene 是否有跟 application 关联
        if (!codeService.applicationRelatedScene(applicationDO.getId(), sceneDO.getId())) {
            throw exception(APPLICATION_SCENE_REL_NOT_EXISTS);
        }

        List<ChannelDO> channelDOS = channelMapper.selectList(new LambdaQueryWrapper<ChannelDO>().in(ChannelDO::getChannelCode, createDTO.getChannelCodes()));
        for (ChannelDO channel : channelDOS) {
            // 检查每个 channel 是否有跟 application 关联
            if (!codeService.applicationRelatedChannel(applicationDO.getId(), channel.getId())) {
                throw exception(APPLICATION_CHANNEL_REL_NOT_EXISTS);
            }
        }
        ApplyDO applyDO = new ApplyDO();
        applyDO.setGeneratedCount(1); // 因为接下来马上就会进行亮码操作，所以这里设置为 1
        applyDO.setApplicationId(applicationDO.getId());
        applyDO.setSceneId(sceneDO.getId());
        applyDO.setEntityId(createDTO.getEntityId());
        applyDO.setEntity(createDTO.getEntity());
        applyDO.setExpiredTime(createDTO.getExpiredTime());
        applyDO.setApplyDescription(createDTO.getApplyDescription());
        applyMapper.insert(applyDO);
        StringBuffer channelIdBuf = new StringBuffer();
        StringBuffer channelCodeBuf = new StringBuffer();
        for (ChannelDO channel : channelDOS) {
            ApplyChannelRelDO applyChannelRelDO = new ApplyChannelRelDO();
            applyChannelRelDO.setApplyId(applyDO.getId());
            applyChannelRelDO.setChannelId(channel.getId());
            channelIdBuf.append(channel.getId()).append(",");
            channelCodeBuf.append(channel.getChannelCode()).append(",");
            applyChannelRelMapper.insert(applyChannelRelDO);
        }
        CodeRecordDO codeRecord = codeService.createCodeRecord(applyDO, applicationDO.getApplicationCodePrefix(), createDTO.getCodeCategory());
        // 亮码记录
        CodeLogDO codeLogDO = new CodeLogDO();
        codeLogDO.setCodeCategory(ApplyCategory.STATIC);
        codeLogDO.setChannelIdStr(StrUtil.replaceLast(channelIdBuf.toString(), ",", ""));
        codeLogDO.setChannelCode(StrUtil.replaceLast(channelCodeBuf.toString(), ",", ""));
        codeLogDO.setCodeLogCategory(CodeLogCategory.GENERATE);
        codeLogDO.setCodeLogAuthId(null);
        codeLogDO.setCodeLogAuthOrderId(null);
        codeLogDO.setCodeLogApplyId(applyDO.getId());
        codeLogDO.setCodeKey(codeRecord.getCodeKey());
        codeLogDO.setLogStatus(CodeLogStatus.SUCCESS);
        codeLogDO.setApplicationId(applicationDO.getId());
        codeLogDO.setSceneId(sceneDO.getId());
        codeLogDO.setEntityId(createDTO.getEntityId());
        codeLogDO.setEntity(createDTO.getEntity());
        codeLogMapper.insert(codeLogDO);
        codeLogDO.setCodeLogOrderId(codeService.createCodeLogOrderId(codeLogDO.getId()));
        codeLogMapper.updateById(codeLogDO);
        CodeRecordGeneratorCodeInfoRespVO VO = new CodeRecordGeneratorCodeInfoRespVO();
        String sceneCodeUrl = sceneDO.getSceneCodeLogo();
        VO.setCode(codeRecord.getCodeUrl());
        VO.setSceneCodeUrl(sceneCodeUrl);
        VO.setCodeImageStr(createQrCodeImageByteStr(sceneCodeUrl, codeRecord.getCodeUrl()));
        return VO;
    }

    @Override
    public CodeRecordGeneratorCodeRespVO createCodeImage(String codeContent, String sceneCodeUrl) {
        log.info("创建二维码图片，codeContent:{}, sceneCodeUrl:{}", codeContent, sceneCodeUrl);
        CodeRecordGeneratorCodeRespVO VO = new CodeRecordGeneratorCodeRespVO();
        VO.setCodeImageBase64(createQrCodeImageByteStr(sceneCodeUrl, codeContent));
        return VO;
    }

    /**
     * 根据 codeUrl 获取 ApplyDO 对象
     *
     * @param codeUrl codeUrl 字符串
     * @return ApplyDO 对象
     */
    private ApplyDO getApplyDO(String codeUrl) {

        CodeRecordDO codeRecord = codeRecordMapper.selectOne(new LambdaQueryWrapperX<CodeRecordDO>()
                .eq(CodeRecordDO::getCodeUrl, codeUrl));

        if (codeRecord == null) {
            throw exception(CODE_RECORD_NOT_EXISTS);
        }

        ApplyDO applyDO = applyMapper.selectById(codeRecord.getApplyId());

        if (applyDO == null) {
            throw exception(APPLY_NOT_EXISTS);
        }

        return applyDO;

    }

}
