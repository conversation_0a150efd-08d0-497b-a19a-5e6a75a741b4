package com.hainancrc.module.codeengine.service.publish;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.hainancrc.module.codeengine.mapper.application.ApplicationMapper;
import com.hainancrc.module.codeengine.mapper.apply.ApplyMapper;
import com.hainancrc.module.codeengine.mapper.coderecord.CodeRecordMapper;

import lombok.var;

import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.*;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;

@Service
public class PublishServiceImpl implements PublishService {

    @Resource
    private CodeRecordMapper codeRecordMapper;

    @Resource
    private ApplicationMapper applicationMapper;

    @Resource
    private ApplyMapper applyMapper;

    @Override
    /**
     * 二维码扫描的入口
     * return: 重定向到新的url
     */
    public String publishEntry(String q, String e) {
        
        var codeRecordDO = codeRecordMapper.selectById(q);
        if (codeRecordDO == null) {
            // 二维码不存在
            throw exception(CODE_RECORD_NOT_EXISTS);
        }

        codeRecordDO.setScanCount(codeRecordDO.getScanCount() + 1);
        codeRecordMapper.updateById(codeRecordDO);

        var applyDO = applyMapper.selectById(codeRecordDO.getApplyId());
        var applicationDO = applicationMapper.selectById(applyDO.getApplicationId());

        
        try {

            return applicationDO.getApplicationCodePrefix() 
                + URLEncoder.encode(codeRecordDO.getCodeUrl(), StandardCharsets.UTF_8.toString());
                
        } catch (UnsupportedEncodingException e1) {

            e1.printStackTrace();
            throw exception(CODE_RECORD_NOT_EXISTS);
        }
        

    }

    
}