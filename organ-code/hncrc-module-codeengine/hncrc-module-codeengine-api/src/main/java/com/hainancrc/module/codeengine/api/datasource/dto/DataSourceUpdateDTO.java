package com.hainancrc.module.codeengine.api.datasource.dto;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("数据源更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DataSourceUpdateDTO extends DataSourceBaseDTO {

    @ApiModelProperty(value = "数据源ID", required = true)
    @NotNull(message = "数据源ID不能为空")
    private Long id;

}
