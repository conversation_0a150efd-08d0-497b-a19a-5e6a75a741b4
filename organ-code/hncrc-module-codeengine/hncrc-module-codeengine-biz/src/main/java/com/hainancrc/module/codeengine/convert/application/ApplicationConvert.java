package com.hainancrc.module.codeengine.convert.application;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.codeengine.api.application.dto.*;
import com.hainancrc.module.codeengine.api.application.vo.*;
import com.hainancrc.module.codeengine.entity.ApplicationDO;

/**
 * 应用 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ApplicationConvert {

    ApplicationConvert INSTANCE = Mappers.getMapper(ApplicationConvert.class);

    ApplicationDO convert(ApplicationCreateDTO bean);

    ApplicationDO convert(ApplicationUpdateDTO bean);

    ApplicationRespVO convert(ApplicationDO bean);

    List<ApplicationRespVO> convertList(List<ApplicationDO> list);

    PageResult<ApplicationRespVO> convertPage(PageResult<ApplicationDO> page);


}
