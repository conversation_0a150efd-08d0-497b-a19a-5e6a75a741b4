package com.hainancrc.module.codeengine.controller.codelog;

import com.hainancrc.module.codeengine.api.codelog.CodeLogApi;
import com.hainancrc.module.codeengine.api.codelog.dto.CodeLogQueryDTO;
import com.hainancrc.module.codeengine.api.codelog.vo.SimpleCodeLogRespVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.annotations.*;
import javax.validation.*;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.common.pojo.CommonResult;
import static com.hainancrc.framework.common.pojo.CommonResult.success;

import com.hainancrc.module.codeengine.api.application.vo.ApplicationRespVO;
import com.hainancrc.module.codeengine.api.codelog.dto.CodeLogPageDTO;
import com.hainancrc.module.codeengine.api.codelog.vo.CodeLogRespVO;
import com.hainancrc.module.codeengine.service.codelog.CodeLogService;

@Api(tags = "码记录")
@RestController
@Validated
public class CodeLogController implements CodeLogApi {

    @Resource
    private CodeLogService codeLogService;

    @GetMapping("/codeengine/codelog/page")
    @ApiOperation("获得码记录分页")
    public CommonResult<PageResult<CodeLogRespVO>> getCodeLogPage(@Valid CodeLogPageDTO pageDTO) {
        return success(codeLogService.getCodeLogPage(pageDTO));
    }

    @GetMapping("/codeengine/codelog/get")
    @ApiOperation("获得码记录")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = String.class)
    public CommonResult<CodeLogRespVO> getCodeLog(@RequestParam("id") String id) {
        return success(codeLogService.getCodeLog(Long.valueOf(id)));
    }

    @Override
    public PageResult<SimpleCodeLogRespVO> queryLog(CodeLogQueryDTO pageDTO) {
        return codeLogService.querySimpleLogPage(pageDTO);
    }
}
