buildTime=$(date "+%Y%m%d%H%M%S")
imageName="${imageName}:${buildTime}"
dockerNamespace=${namespace}
k8sConfig=${k8sDeployConfig}
kubeConfigPath="/opt/organ/.kube/config"

# 引用Jenkins凭证中的用户名和密码
DOCKER_LOGIN_NAME=${DOCKER_LOGIN_NAME}
DOCKER_LOGIN_PASSWORD=${DOCKER_LOGIN_PASSWORD}
delimiter="-"

set -e

buildImage() {
	docker build -t $imageName .
	echo $DOCKER_LOGIN_PASSWORD | docker login -u $DOCKER_LOGIN_NAME --password-stdin harbor.ffcs.cn
	echo $DOCKER_LOGIN_PASSWORD | docker login -u $DOCKER_LOGIN_NAME --password-stdin 10.111.240.24
	docker push $imageName
}

doK8sDeploy() {
	if [ ! -n "$k8sConfig" ]; then delimiter="" kubeConfigPath="/opt/.kube/config"; fi
	sed -i "s!image_build_name!$imageName!g; s!docker_namespace!$dockerNamespace!g" config/deploy$delimiter$k8sConfig.yaml
	kubectl --kubeconfig $kubeConfigPath apply -f config/deploy$delimiter$k8sConfig.yaml -n $dockerNamespace
}

buildImage
doK8sDeploy
