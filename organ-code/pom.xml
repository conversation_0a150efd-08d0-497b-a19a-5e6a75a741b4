<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>hainancrc</groupId>
  <artifactId>hncrc-code</artifactId>
  <version>1.0-SNAPSHOT</version>
  <packaging>pom</packaging>

  <name>hncrc-code</name>
  <url>http://maven.apache.org</url>
  <modules>
    <module>hncrc-module-codeengine</module>
  </modules>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <revision>1.0-SNAPSHOT</revision>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>hainancrc</groupId>
        <artifactId>hncrc-dependency</artifactId>
        <version>${revision}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
