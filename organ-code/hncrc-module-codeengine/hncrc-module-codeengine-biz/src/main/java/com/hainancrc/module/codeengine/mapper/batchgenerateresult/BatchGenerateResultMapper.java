package com.hainancrc.module.codeengine.mapper.batchgenerateresult;

import java.util.*;

import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.module.codeengine.entity.BatchGenerateResultDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 批量生成结果 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BatchGenerateResultMapper extends BaseMapperX<BatchGenerateResultDO> {


}
