package com.hainancrc.module.codeengine.api.applicationscenerel.dto;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 应用场景多对多
*/
@Data
public class ApplicationSceneRelBaseDTO {

    @ApiModelProperty(value = "应用ID", required = true)
    @NotNull(message = "应用ID不能为空")
    private Integer applicationId;

    @ApiModelProperty(value = "场景ID", required = true)
    @NotNull(message = "场景ID不能为空")
    private Integer sceneId;

}
