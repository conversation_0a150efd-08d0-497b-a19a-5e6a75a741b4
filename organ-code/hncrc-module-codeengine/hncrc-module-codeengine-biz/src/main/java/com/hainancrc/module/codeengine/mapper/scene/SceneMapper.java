package com.hainancrc.module.codeengine.mapper.scene;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.codeengine.entity.SceneDO;
import com.hainancrc.module.codeengine.mapper.CodeBaseMapper;

import org.apache.ibatis.annotations.Mapper;
import com.hainancrc.module.codeengine.api.scene.dto.*;

/**
 * 场景 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SceneMapper extends CodeBaseMapper<SceneDO> {

    default PageResult<SceneDO> selectPage(ScenePageDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<SceneDO>()
                .likeIfPresent(SceneDO::getSceneName, reqDTO.getSceneName())
                .betweenIfPresent(SceneDO::getCreateTime, reqDTO.getCreateTimeStart(), reqDTO.getCreateTimeEnd())
                .orderByDesc(SceneDO::getId));
    }

    default SceneDO selectByCode(String code){
        return selectOne(new LambdaQueryWrapperX<SceneDO>().eq(SceneDO::getSceneCode, code));
    }

    default Boolean existsByName(String name){
        return exists(new LambdaQueryWrapperX<SceneDO>().eq(SceneDO::getSceneName, name));
    }

    default SceneDO selectByName(String name){
        return selectOne(new LambdaQueryWrapperX<SceneDO>().eq(SceneDO::getSceneName, name));
    }

}
