package com.organ.module.canteen.mealquota.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@TableName("canteen_meal_quota")
@EqualsAndHashCode(callSuper = true)
public class MealQuotaDO extends BaseDO {

    @TableId
    private Long id;

    /**
     * 餐次id
     */
    private Long mealPeriodId;

    /**
     * 日期
     */
    private Date quotaDate;

    /**
     * 配额
     */
    private Integer quotaCount;

    /**
     * 剩余配额
     */
    private Integer quotaLeft;

    /**
     * 数据状态（UNPUBLISHED-未发布 PUBLISHED-已发布）
     */
    private String publishStatus;

    /**
     * 发布人
     */
    private String publisher;

    /**
     * 发布时间
     */
    private String publishTime;
}
