package com.hainancrc.module.codeengine.api.publicapi.vo;

import com.hainancrc.module.codeengine.enums.CodeLogStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("ApplyCreateRespVO")
@Data
public class ApplyCreateRespVO {

    @ApiModelProperty(value = "申码订单ID", required = true)
    private String applyOrderId;

    @ApiModelProperty(value = "申码状态(authRequired/fail/success)", required = true)
    private CodeLogStatus applyStatus;

    @ApiModelProperty(value = "申码授权动作ID", required = false)
    private String authActionId;
    
}