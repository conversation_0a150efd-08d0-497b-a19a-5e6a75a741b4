
--顺序执行
alter table creditcode_credit_scene add <PERSON><PERSON>UM<PERSON> up TINYINT(1) not null default '1' comment '上下架' after browse_num;

update creditcode_credit_scene a set a.up = 0 where is_enable = 0;

alter table creditcode_ad add COLUMN up TINYINT(1) not null default '1' comment '上下架' after browse_num;

update creditcode_ad set up = 0 where is_enable = 0;

alter table creditcode_ad_class add COLUMN up TINYINT(1) not null default '1' comment '上下架' after sort;

alter table codeengine_code_record add column apply_category varchar(32) not null default '' COMMENT '申码分类(dynamic/static)' after code_url;

update codeengine_code_record a set apply_category = (select apply_category from codeengine_apply b where a.apply_id = b.id)

alter table codeengine_code_log add column validity_time datetime DEFAULT NULL COMMENT '有效期' after log_status;

alter table codeengine_code_log add column create_name varchar(32) DEFAULT '' COMMENT '用户名' after creator;

alter table codeengine_code_log add column code_category varchar(16) DEFAULT NULL COMMENT '码类型：dynamic/static' after log_status;

update codeengine_code_log a set code_category = (select apply_category from codeengine_apply b where a.code_log_apply_id = b.id);

alter table codeengine_apply drop column apply_category;