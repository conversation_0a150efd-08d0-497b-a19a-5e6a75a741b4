package com.hainancrc.module.codeengine.controller.upload;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.upload.api.upload.UploadFileApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Api(tags = "文件上传")
@RestController
@RequestMapping("/codeengine/upload")
@Validated
public class UploadController {
    @Resource
    private UploadFileApi uploadFileApi;

    /**
     * 上传文件接口
     *
     * @param file 文件
     * @return
     */
    @PostMapping("/staticUpload")
    @ApiOperation("文件上传接口")
    public CommonResult upload(@ApiParam(value = "文件流", required = true) @RequestPart("file") MultipartFile file,
            HttpServletRequest request) {
        System.out.println(request.getHeader("Authorization"));
        return uploadFileApi.staticUpload(file);
    }

}
