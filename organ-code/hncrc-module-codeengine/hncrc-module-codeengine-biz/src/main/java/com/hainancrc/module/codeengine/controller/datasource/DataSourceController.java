package com.hainancrc.module.codeengine.controller.datasource;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.annotations.*;

import javax.validation.*;
import java.util.*;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.common.pojo.CommonResult;
import static com.hainancrc.framework.common.pojo.CommonResult.success;

import com.hainancrc.module.codeengine.api.common.IdNameVO;
import com.hainancrc.module.codeengine.api.datasource.dto.*;
import com.hainancrc.module.codeengine.api.datasource.vo.*;
import com.hainancrc.module.codeengine.entity.DataSourceDO;
import com.hainancrc.module.codeengine.convert.datasource.DataSourceConvert;
import com.hainancrc.module.codeengine.service.datasource.DataSourceService;

@Api(tags = "数据源")
@RestController
@RequestMapping("/codeengine/data-source")
@Validated
public class DataSourceController {

    @Resource
    private DataSourceService dataSourceService;

    @GetMapping("/keyValue")
    @ApiOperation("以 key-value 方式获取渠道内容")
    public CommonResult<List<IdNameVO>> getKeyValue() {
        return success(dataSourceService.getKeyValue());
    }

    @PostMapping("/create")
    @ApiOperation("创建数据源")
    public CommonResult<Long> createDataSource(@Valid @RequestBody DataSourceCreateDTO createDTO) {
        return success(dataSourceService.createDataSource(createDTO));
    }

    @PutMapping("/update")
    @ApiOperation("更新数据源")
    public CommonResult<Boolean> updateDataSource(@Valid @RequestBody DataSourceUpdateDTO updateDTO) {
        dataSourceService.updateDataSource(updateDTO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除数据源")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> deleteDataSource(@RequestParam("id") Long id) {
        dataSourceService.deleteDataSource(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得数据源")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<DataSourceRespVO> getDataSource(@RequestParam("id") Long id) {
        DataSourceDO dataSource = dataSourceService.getDataSource(id);
        return success(DataSourceConvert.INSTANCE.convert(dataSource));
    }

    @GetMapping("/list")
    @ApiOperation("获得数据源列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    public CommonResult<List<DataSourceRespVO>> getDataSourceList(@RequestParam("ids") Collection<Long> ids) {
        List<DataSourceDO> list = dataSourceService.getDataSourceList(ids);
        return success(DataSourceConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得数据源分页")
    public CommonResult<PageResult<DataSourceRespVO>> getDataSourcePage(@Valid DataSourcePageDTO pageDTO) {
        PageResult<DataSourceDO> pageResult = dataSourceService.getDataSourcePage(pageDTO);
        return success(DataSourceConvert.INSTANCE.convertPage(pageResult));
    }

    @PostMapping("/test")
    @ApiOperation("数据源链接测试")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<String> testDataSource(@RequestParam("id") Long id) {
        return success(dataSourceService.testDataSource(id));
    }

}
