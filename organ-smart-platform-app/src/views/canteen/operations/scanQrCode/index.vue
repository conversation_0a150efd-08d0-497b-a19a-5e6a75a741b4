<template>
  <div class="scan-qr-code-page">
    <div class="header-wrapper">
      <MobileHeader title="扫码就餐" bgColor="transparent" />
      <div class="header-gap"></div>
    </div>

    <div class="canteen-content-pd page-content">
      <!-- 扫描操作 -->
      <ScanOperate v-if="state === ConsumeStateEnum.SCAN" class="height-full" />
      <!-- 扫描结果 -->
      <ScanResult
        v-else-if="state === ConsumeStateEnum.RESULT"
        :result="scanResult"
        class="height-full"
        @rescan="handleRescanDiningCode"
        @continue-scan="handleContinueScanDiningCode"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import MobileHeader from "@/components/MobileHeader/index.vue";
import { IConsumeResult } from "@/api/canteen/operations/types/consume";
import ScanOperate from "./_comp/scanOperate.vue";
import ScanResult from "./_comp/scanResult.vue";
import { consumeDiningCode } from "@/api/canteen/operations/consume";

/** 核销状态枚举 */
enum ConsumeStateEnum {
  /** 扫描 */
  SCAN = "scan",
  /** 结果 */
  RESULT = "result",
}

/** 当前扫描结果 */
const scanResult = ref<IConsumeResult>();

/** 当前状态 */
// TODO: dev测试数据
const state = ref<ConsumeStateEnum>(ConsumeStateEnum.SCAN);

/** 当前是否在核销中 */
const consuming = ref(false);

/** 核销就餐码 */
const handleConsumeDiningCode = async (code: string) => {
  try {
    consuming.value = true;
    const result = await consumeDiningCode(code);
    scanResult.value = result;
    state.value = ConsumeStateEnum.RESULT;
  } catch (error) {
    console.error("核销就餐码失败:", error);
  } finally {
    consuming.value = false;
  }
};

/** 重新扫码 */
const handleRescanDiningCode = () => {
  state.value = ConsumeStateEnum.SCAN;
};

/** 继续扫码 */
const handleContinueScanDiningCode = () => {
  state.value = ConsumeStateEnum.SCAN;
};
</script>

<style scoped lang="scss">
.scan-qr-code-page {
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;

  .header-wrapper {
    flex-shrink: 0;
    flex-grow: 0;
  }

  .page-content {
    padding-top: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(
      100vh - var(--header-height) - var(--tabbar-height) -
        env(safe-area-inset-top)
    );
    overflow: hidden;
  }

  .height-full {
    flex-grow: 1;
  }
}
</style>
