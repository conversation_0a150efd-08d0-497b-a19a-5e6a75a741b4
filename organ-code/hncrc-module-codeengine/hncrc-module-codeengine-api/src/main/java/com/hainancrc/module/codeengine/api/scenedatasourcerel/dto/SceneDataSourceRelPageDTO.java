package com.hainancrc.module.codeengine.api.scenedatasourcerel.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import com.hainancrc.framework.common.pojo.PageParam;


@ApiModel("场景数据源分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SceneDataSourceRelPageDTO extends PageParam {


    @ApiModelProperty(value = "场景ID")
    private Integer sceneId;

    @ApiModelProperty(value = "数据源ID")
    private Integer dataSourceId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
