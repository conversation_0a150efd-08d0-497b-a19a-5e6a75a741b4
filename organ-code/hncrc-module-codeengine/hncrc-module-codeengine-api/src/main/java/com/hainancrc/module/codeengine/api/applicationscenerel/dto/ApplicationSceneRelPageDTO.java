package com.hainancrc.module.codeengine.api.applicationscenerel.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import com.hainancrc.framework.common.pojo.PageParam;


@ApiModel("应用场景多对多分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApplicationSceneRelPageDTO extends PageParam {


    @ApiModelProperty(value = "应用ID")
    private Integer applicationId;

    @ApiModelProperty(value = "场景ID")
    private Integer sceneId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
