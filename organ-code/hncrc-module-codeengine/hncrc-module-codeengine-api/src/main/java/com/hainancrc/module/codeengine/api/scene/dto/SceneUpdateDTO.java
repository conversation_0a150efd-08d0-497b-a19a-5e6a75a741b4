package com.hainancrc.module.codeengine.api.scene.dto;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("场景更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SceneUpdateDTO extends SceneCreateDTO {

    @ApiModelProperty(value = "场景ID", required = true)
    @NotNull(message = "场景ID不能为空")
    private Long id;

    @ApiModelProperty(value = "数据源Id")
    private Long[] dataSourceId;

    @ApiModelProperty(value = "应用Id")
    private Long[] applicationId;

}
