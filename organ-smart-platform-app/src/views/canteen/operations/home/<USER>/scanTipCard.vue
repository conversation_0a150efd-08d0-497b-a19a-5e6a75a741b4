<template>
  <div class="scan-tip-card" @click.stop="handleScanDiningCode">
    <div class="icon-wrapper">
      <img class="img-icon" src="@/assets/images/canteen/code_h.png" />
    </div>
    <div class="content">
      <div class="title">扫码就餐</div>
      <div class="mb-10">扫描就餐码进行核销</div>
      <div class="tip-with-icon">
        点击进入扫码页面
        <img class="icon" src="@/assets/images/canteen/play.png" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import router from "@/router";
import { BIZ_MODULE_ROUTE_PREFIX } from "@/views/canteen/configs/constants";

/** 点击扫码就餐 */
const handleScanDiningCode = () => {
  router.push({
    path: `${BIZ_MODULE_ROUTE_PREFIX}/operations/scanQrCode`,
  });
};
</script>

<style scoped lang="scss">
$imgIconH: 36px;

.scan-tip-card {
  display: flex;
  align-items: flex-start;
  background: #ffffff;
  border-radius: 8px;
  padding: 20px 12px;
  cursor: pointer;

  .icon-wrapper {
    width: $imgIconH;
    height: $imgIconH;
    margin-right: 12px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border-radius: 10px;
    background-color: #eff6ff;

    .img-icon {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .content {
    color: #808080;

    .title {
      height: $imgIconH;
      line-height: $imgIconH;
      font-size: 20px;
      color: #000000;
      margin-bottom: 10px;
    }

    .tip-with-icon {
      display: flex;
      align-items: center;
    }

    .icon {
      width: 14px;
      height: 14px;
      object-fit: contain;
      margin-left: 16px;
    }
  }

  .mb-10 {
    margin-bottom: 10px;
  }
}
</style>
