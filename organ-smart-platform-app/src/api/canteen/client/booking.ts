import service from "@/service";
import { BIZ_MODULE_API_PREFIX } from "@/api/canteen/constant";
import {
  IBatchBookingRequest,
  IBatchBookingResult,
  IUserBooking,
} from "./types/booking";

/** 模块API前缀 */
const MODULE_API_PREFIX = `${BIZ_MODULE_API_PREFIX}/booking`;

/** 是否模拟数据 */
const isMock = true;

/** 分页获取用户预约记录 */
export function getUserBookingPage(params: PaginatedRequestParams) {
  if (isMock) {
    console.log("getUserBookingPage", params);
    return new Promise<PaginatedResponseData<IUserBooking>>((resolve) => {
      // 根据传入的页码和每页大小生成模拟数据，最大不操作55条数据
      const total = 55;
      const start = (params.pageNo - 1) * params.pageSize;
      const list = Array.from({ length: params.pageSize }, (_, index) => ({
        id: `${start + index + 1}`,
        canteenName: "学苑食堂" + start + index,
        mealName: ["午餐", "晚餐"][index % 2],
        bookingCode: "66666-" + start + index,
        mealTime: "11:00-13:00",
        bookingDate: "2025-08-28",
        status: ["CONFIRMED", "CANCELLED", "VERIFIED", "EXPIRED"][index % 4],
        statusLabel: ["待使用", "已取消", "已使用", "已失效"][index % 4],
      }));
      resolve({
        total,
        list,
      });
    });
  }
  return service.get<any, PaginatedResponseData<IUserBooking>>(
    `${MODULE_API_PREFIX}/userPages`,
    {
      params,
    }
  );
}

/**
 * 批量预约
 */
export function batchBooking(params: IBatchBookingRequest) {
  if (isMock) {
    return new Promise<IBatchBookingResult>((resolve) => {
      resolve({
        success: true,
        message: "预约成功",
        successMeals: params.mealPeriods.map((item) => ({
          mealPeriodId: item.mealPeriodIds[0],
          mealName: "午餐",
          mealTime: "11:00-13:00",
          bookingDate: item.bookingDate,
        })),
        // 当天的第一个预约失败
        failedMeals: params.mealPeriods
          .filter((item, index) => index === 0)
          .map((item) => ({
            mealPeriodId: item.mealPeriodIds[0],
            mealTime: "11:00-13:00",
            mealName: "午餐",
            reason: "席位已满",
            bookingDate: item.bookingDate,
          })),
        totalCount: params.mealPeriods.length,
        successCount: params.mealPeriods.length,
        failedCount: 1,
      });
    });
  }
  return service.post<IBatchBookingRequest, IBatchBookingResult>(
    `${MODULE_API_PREFIX}/batch`,
    params
  );
}

/** 获取当前最近待使用的预约信息 */
export function getStartingSoonBooking() {
  if (isMock) {
    return new Promise<IUserBooking>((resolve) => {
      setTimeout(() => {
        resolve({
          id: "1",
          canteenName: "学苑食堂",
          mealName: "午餐",
          bookingDate: "2024-08-28",
          mealTime: "11:00-13:00",
          bookingCode: "66666",
          status: "CONFIRMED",
          statusLabel: "待使用",
        });
      }, 1000);
    });
  }
  return service.get<any, IUserBooking>(`${MODULE_API_PREFIX}/startingSoon`);
}

/** 获取的的预约数 */
export function getMyBookingCount() {
  if (isMock) {
    return new Promise<number>((resolve) => {
      resolve(10);
    });
  }
  return service.get<any, number>(`${MODULE_API_PREFIX}/myBookingCount`);
}
