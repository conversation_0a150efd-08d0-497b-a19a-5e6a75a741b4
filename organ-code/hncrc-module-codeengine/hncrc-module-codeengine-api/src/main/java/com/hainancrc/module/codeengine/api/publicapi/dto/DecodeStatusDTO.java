package com.hainancrc.module.codeengine.api.publicapi.dto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("解码授权查询 Request VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DecodeStatusDTO{

    @ApiModelProperty(value = "应用Code", required = true)
    @NotNull(message = "应用Code不能为空")
    private String application;

    @ApiModelProperty(value = "解码订单号")
    @NotEmpty(message = "亮码订单号不能为空")
    private String decodeOrderId;

}