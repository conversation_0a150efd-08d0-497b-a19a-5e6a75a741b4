package com.hainancrc.module.codeengine.api.publicapi.dto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("解码授权验证 Request VO")
@Data
public class DecodeWithAuthDTO {

    @ApiModelProperty(value = "应用Code", required = true)
    @NotNull(message = "应用Code不能为空")
    private String application;

    @ApiModelProperty(value = "解码订单号")
    @NotEmpty(message = "解码订单号不能为空")
    private String decodeOrderId;

    @ApiModelProperty(value = "授权订单号", required = false)
    private String authOrderId;

}