package com.hainancrc.module.codeengine.controller.coderecord;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.codeengine.api.coderecord.CodeRecordCreateDTO;
import com.hainancrc.module.codeengine.api.coderecord.vo.BatchCodeRecordGeneratorCodeRespVO;
import com.hainancrc.module.codeengine.api.coderecord.vo.CodeRecordGeneratorCodeRespVO;
import com.hainancrc.module.codeengine.service.coderecord.CodeRecordService;

import java.io.IOException;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;

import io.swagger.annotations.*;
import lombok.var;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

@Api(tags = "生码")
@RestController
@RequestMapping("/codeengine/code")
@Validated
public class CodeRecordController {

    @Resource
    private CodeRecordService codeRecordService;

    @PostMapping("/generator")
    @ApiOperation("生成码")
    public CommonResult<CodeRecordGeneratorCodeRespVO> generatorCode(@Valid @RequestBody CodeRecordCreateDTO createDTO) {
        return success(codeRecordService.createCodeImage(createDTO));
    }

    @PostMapping("/generatorBatch")
    @ApiOperation("批量生成码")
    public CommonResult<BatchCodeRecordGeneratorCodeRespVO> generatorCode(
        @RequestParam("applicationId") Long applicationId,
        @RequestParam("file") MultipartFile file) throws IOException {
            
        return success(codeRecordService.createCodeBatch(applicationId, file.getBytes()));
    }

    @GetMapping("/downloadBatch")
    @ApiOperation("下载批量生成码")
    public ResponseEntity<byte[]> downloadBatch(
        @RequestParam("generateOrderId") String generateOrderId) throws IOException {
            
        var buff = codeRecordService.getCodesByGenerateOrderId(generateOrderId);

        var fileName = codeRecordService.getDownloadNameFromGenerateOrderId(generateOrderId);
        var headers = new HttpHeaders();
        
        headers.add("Content-Disposition", "attachment;filename=" + fileName);

        return ResponseEntity.ok().headers(headers).body(buff);
    }



    

}
