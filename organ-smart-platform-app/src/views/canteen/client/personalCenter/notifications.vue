<template>
  <div class="my-nitifications-page">
    <MobileHeader title="消息中心" bgColor="transparent">
      <template #right>
        <div
          class="canteen-link mark-all-read"
          :class="{ disabled: isMarkAllReadDisabled }"
          @click="handleMarkAllRead"
        >
          全部已读
        </div>
      </template>
    </MobileHeader>
    <div class="header-gap"></div>
    <div class="canteen-content-px">
      <div class="type-selector">
        <div
          v-for="item in notificationTypeList"
          :key="item.value"
          class="type-item"
          :class="{ active: activeType === item.value }"
          @click.stop="handleSwitchType(item.value)"
        >
          {{ item.label }}
          <div class="under-line"></div>
        </div>
      </div>
    </div>
    <div class="canteen-list-page canteen-content-px" style="padding-top: 0">
      <div class="list-content">
        <AutoLoad
          :loading="loading"
          :refreshing="refreshing"
          :has-more="hasMore"
          :show-empty="isEmpty"
          @load-more="loadMore"
          @refresh="refresh"
        >
          <template v-for="item in data">
            <NotificationCard
              class="mb-20"
              :key="item.id"
              :notificationInfo="item"
            />
          </template>

          <template #empty>
            <div class="empty-state">
              <div class="empty-icon">📋</div>
              <h3>暂无数据</h3>
              <p>点击刷新按钮加载数据</p>
              <button @click="refresh" class="refresh-btn">刷新数据</button>
            </div>
          </template>
        </AutoLoad>
      </div>
    </div>
    <TabBar />
  </div>
</template>

<script setup lang="ts">
import MobileHeader from "@/components/MobileHeader/index.vue";
import TabBar from "@/components/TabBar/index.vue";
import NotificationCard from "./_comp/notifications/notificationCard.vue";
import AutoLoad from "@/components/AutoLoad";
import { useAutoLoad } from "@/components/AutoLoad";
import { LoadDataParams } from "@/components/AutoLoad/types";
import {
  getMyNotificationPage,
  readAllNotification,
} from "@/api/canteen/client/notification";
import { NotificationTypeEnum } from "../../configs/enums/NotificationTypeEnum";

const { data, loading, refreshing, hasMore, isEmpty, loadMore, refresh } =
  useAutoLoad({
    pageSize: 10,
    loadData: async (
      params: LoadDataParams<{ isRead?: boolean; notificationType: string }>
    ) => {
      // 模拟 API 请求
      return getMyNotificationPage({
        ...params,
        notificationType: activeType.value,
      }).then((res) => {
        return {
          data: res.list,
          total: res.total,
        };
      });
    },
    onError: (error) => {
      console.error("Hook 加载失败:", error);
    },
  });

/** 全部已读是否可点击 */
const isMarkAllReadDisabled = computed(() => {
  return data.value.every((item) => item.isRead);
});

/** 消息类型列表 */
const notificationTypeList = [
  { label: "系统消息", value: NotificationTypeEnum.SYSTEM_MSG },
  { label: "通知公告", value: NotificationTypeEnum.ANNOUNCEMENT },
];

/** 消息类型 */
const activeType = ref(NotificationTypeEnum.SYSTEM_MSG);

/** 切换消息类型 */
const handleSwitchType = (type: NotificationTypeEnum) => {
  activeType.value = type;
  refresh();
};

/** 是否正在提交 */
const submitting = ref(false);

/** 标记全部已读 */
const handleMarkAllRead = async () => {
  try {
    if (isMarkAllReadDisabled.value) return;
    if (submitting.value) return;
    submitting.value = true;
    await readAllNotification();
    refresh();
  } catch (error) {
    console.error("标记全部已读失败:", error);
  } finally {
    submitting.value = false;
  }
};
</script>

<style scoped lang="scss">
$typeSelectorHeight: 40px;

.my-nitifications-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  overflow-y: hidden !important;

  .list-content {
    // padding: 0 20px;
    margin-top: 20px;
    height: calc(100% - #{$typeSelectorHeight} - 20px);
  }

  .type-selector {
    height: $typeSelectorHeight;
    display: flex;
    align-items: center;
    column-gap: 20px;
    padding: 10px;
    border-radius: 4px;
    background-color: #ffffff;

    .type-item {
      text-align: center;
      font-size: 15px;
      color: #808080;
      cursor: pointer;

      .under-line {
        display: block;
        width: 0;
        height: 2px;
        background-color: transparent;
        transition: all 0.3s ease;
      }

      &.active {
        color: var(--color-primary);
        .under-line {
          width: 100%;
          background-color: var(--color-primary);
        }
      }

      &:hover {
        color: var(--color-primary);
        .under-line {
          width: 100%;
          background-color: var(--color-primary);
        }
      }
    }
  }

  .mark-all-read {
    font-size: 14px;
  }

  .mb-20 {
    margin-bottom: 10px;
  }
}
</style>
