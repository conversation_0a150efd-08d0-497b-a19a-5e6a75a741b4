package com.hainancrc.module.codeengine.service.scene;

import java.util.*;
import javax.validation.*;

import com.hainancrc.module.codeengine.api.common.IdNameVO;
import com.hainancrc.module.codeengine.api.scene.dto.*;
import com.hainancrc.module.codeengine.api.scene.vo.*;
import com.hainancrc.module.codeengine.entity.SceneDO;
import com.hainancrc.framework.common.pojo.PageResult;

/**
 * 场景 Service 接口
 *
 * <AUTHOR>
 */
public interface SceneService {


    /**
     * 以 key-value pair 方式获取场景内容
     */
    List<IdNameVO> getKeyValue(Integer status);

    /**
     * 创建场景
     *
     * @param createDTO 创建信息
     * @return 编号
     */
    Long createScene(@Valid SceneCreateDTO createDTO);

    /**
     * 更新场景
     *
     * @param updateDTO 更新信息
     */
    void updateScene(@Valid SceneUpdateDTO updateDTO);

    /**
     * 获得场景
     *
     * @param id 编号
     * @return 场景
     */
    SceneRespVO getScene(Long id);

    /**
     * 获得场景列表
     *
     * @param ids 编号
     * @return 场景列表
     */
    List<SceneDO> getSceneList(Collection<Long> ids);

    /**
     * 获得场景分页
     *
     * @param pageDTO 分页查询
     * @return 场景分页
     */
    PageResult<SceneDO> getScenePage(ScenePageDTO pageDTO);

    /**
     * 启用场景
     * @param id
     * @return
     */
    Long enableScene(Long id);

    /**
     * 禁用场景
     * @param id
     * @return
     */
    Long disableScene(Long id);

    /**
     * 关联应用
     */
    void relateApplication(Long id, String applicationIds);

    /**
     * 关联数据源
     */
    void relateDataSource(Long id, String dataSourceIds);

}
