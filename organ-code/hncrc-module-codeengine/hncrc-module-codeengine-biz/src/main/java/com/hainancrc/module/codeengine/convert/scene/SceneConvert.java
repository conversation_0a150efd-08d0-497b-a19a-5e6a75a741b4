package com.hainancrc.module.codeengine.convert.scene;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.codeengine.api.scene.dto.*;
import com.hainancrc.module.codeengine.api.scene.vo.*;
import com.hainancrc.module.codeengine.entity.SceneDO;

/**
 * 场景 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SceneConvert {

    SceneConvert INSTANCE = Mappers.getMapper(SceneConvert.class);

    SceneDO convert(SceneCreateDTO bean);

    SceneDO convert(SceneUpdateDTO bean);

    SceneRespVO convert(SceneDO bean);

    List<SceneRespVO> convertList(List<SceneDO> list);

    PageResult<SceneRespVO> convertPage(PageResult<SceneDO> page);


}
