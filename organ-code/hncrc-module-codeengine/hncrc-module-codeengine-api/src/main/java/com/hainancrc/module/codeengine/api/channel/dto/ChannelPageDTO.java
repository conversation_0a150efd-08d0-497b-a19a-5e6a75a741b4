package com.hainancrc.module.codeengine.api.channel.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import com.hainancrc.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;


@ApiModel("渠道分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChannelPageDTO extends PageParam {

    @ApiModelProperty(value = "创建时间开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;

}
