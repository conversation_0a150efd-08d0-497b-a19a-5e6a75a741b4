package com.hainancrc.module.codeengine.api.scenedatasourcerel.dto;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 场景数据源
*/
@Data
public class SceneDataSourceRelBaseDTO {

    @ApiModelProperty(value = "场景ID", required = true)
    @NotNull(message = "场景ID不能为空")
    private Integer sceneId;

    @ApiModelProperty(value = "数据源ID", required = true)
    @NotNull(message = "数据源ID不能为空")
    private Integer dataSourceId;

}
