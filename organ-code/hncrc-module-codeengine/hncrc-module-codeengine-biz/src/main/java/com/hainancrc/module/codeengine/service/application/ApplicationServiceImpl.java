package com.hainancrc.module.codeengine.service.application;

import com.alibaba.fastjson.JSON;
import com.hainancrc.framework.redis.service.RedisService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.hainancrc.module.codeengine.api.application.dto.*;
import com.hainancrc.module.codeengine.api.application.vo.*;
import com.hainancrc.module.codeengine.api.common.IdNameVO;
import com.hainancrc.module.codeengine.entity.ApplicationChannelRelDO;
import com.hainancrc.module.codeengine.entity.ApplicationDO;
import com.hainancrc.module.codeengine.entity.ApplicationSceneRelDO;
import com.hainancrc.module.codeengine.entity.ChannelDO;
import com.hainancrc.module.codeengine.entity.SceneDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hainancrc.framework.common.pojo.PageResult;

import com.hainancrc.module.codeengine.convert.application.ApplicationConvert;
import com.hainancrc.module.codeengine.mapper.application.ApplicationMapper;
import com.hainancrc.module.codeengine.mapper.applicationchannelrel.ApplicationChannelRelMapper;
import com.hainancrc.module.codeengine.mapper.applicationscenerel.ApplicationSceneRelMapper;
import com.hainancrc.module.codeengine.mapper.channel.ChannelMapper;
import com.hainancrc.module.codeengine.mapper.scene.SceneMapper;
import com.hainancrc.module.codeengine.mapper.scenedatasourcerel.SceneDataSourceRelMapper;

import lombok.var;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.*;
import static com.hainancrc.module.codeengine.utils.Helper.toLongArray;
/**
 * 应用 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ApplicationServiceImpl implements ApplicationService {

    @Resource
    private ApplicationMapper applicationMapper;

    @Resource
    private SceneMapper sceneMapper;

    @Resource
    private ChannelMapper channelMapper;

    @Resource
    private ApplicationChannelRelMapper applicationChannelRelMapper;

    @Resource
    private ApplicationSceneRelMapper applicationSceneRelMapper;

    @Resource
    private SceneDataSourceRelMapper sceneDataSourceRelMapper;

    private static final String APPLICATION_CACHE_KEY = "code:engine:application:%s";

    @Resource
    private RedisService redisService;

    @Override
    public Long createApplication(ApplicationCreateDTO createDTO) {

        this.validateApplicationNotExists(createDTO.getApplicationName());
        this.validateChannelAndSceneExists(createDTO.getSceneId(), createDTO.getChannelId());

        var application = new ApplicationDO();
        application.setApplicationName(createDTO.getApplicationName());
        application.setApplicationDescription(createDTO.getApplicationDescription());
        application.setApplicationEnable(false);
        application.setApplicationCodePrefix(createDTO.getApplicationCodePrefix());

        applicationMapper.insert(application);
        var code = "ZX" + String.format("%07d", application.getId());
        application.setApplicationCode(code);

        applicationMapper.updateById(application);

        for(var channelId : createDTO.getChannelId()){
            var rel = new ApplicationChannelRelDO();
            rel.setApplicationId(application.getId());
            rel.setChannelId(channelId);
            applicationChannelRelMapper.insert(rel);
        }

        for(var sceneId : createDTO.getSceneId()){
            var rel = new ApplicationSceneRelDO();
            rel.setApplicationId(application.getId());
            rel.setSceneId(sceneId);
            applicationSceneRelMapper.insert(rel);
        }
        redisService.set(String.format(APPLICATION_CACHE_KEY,code), JSON.toJSONString(application));
        // 返回
        return application.getId();
    }

    @Override
    public void updateApplication(ApplicationUpdateDTO updateDTO) {
        // 校验存在
        this.validateApplicationExists(updateDTO.getId());
        this.validateChannelAndSceneExists(updateDTO.getSceneId(), updateDTO.getChannelId());

        // 更新
        ApplicationDO updateObj = ApplicationConvert.INSTANCE.convert(updateDTO);
        applicationMapper.updateById(updateObj);

        // 删除关联
        applicationChannelRelMapper.delete(
            new LambdaQueryWrapper<ApplicationChannelRelDO>()
                .eq(ApplicationChannelRelDO::getApplicationId, updateDTO.getId())
        );
        applicationSceneRelMapper.delete(
            new LambdaQueryWrapper<ApplicationSceneRelDO>()
                .eq(ApplicationSceneRelDO::getApplicationId, updateDTO.getId())
        );

        // 重新关联
        for(var channelId : updateDTO.getChannelId()){
            var rel = new ApplicationChannelRelDO();
            rel.setApplicationId(updateDTO.getId());
            rel.setChannelId(channelId);
            applicationChannelRelMapper.insert(rel);
        }

        for(var sceneId : updateDTO.getSceneId()){
            var rel = new ApplicationSceneRelDO();
            rel.setApplicationId(updateDTO.getId());
            rel.setSceneId(sceneId);
            applicationSceneRelMapper.insert(rel);
        }
        redisService.set(String.format(APPLICATION_CACHE_KEY,updateObj.getApplicationCode()),JSON.toJSONString(updateObj));
    }

    private void validateChannelAndSceneExists(Long[] sceneId, Long[] channelId) {

        if (sceneId != null) {
            for (Long id : sceneId) {
                if (sceneMapper.selectById(id) == null) {
                    throw exception(SCENE_NOT_EXISTS);
                }
            }
        }

        if (channelId != null) {
            for (Long id : channelId) {
                if (channelMapper.selectById(id) == null) {
                    throw exception(CHANNEL_NOT_EXISTS);
                }
            }
        }

    }

    private void validateApplicationExists(Long id) {
        if (applicationMapper.selectById(id) == null) {
            throw exception(APPLICATION_NOT_EXISTS);
        }
    }

    private void validateApplicationNotExists(String name){
        if(applicationMapper.existsByName(name)){
            throw exception(APPLICATION_NAME_EXISTS);
        }
    }

    @Override
    public ApplicationRespVO getApplication(Long id) {
        var DO = applicationMapper.selectById(id);
        var VO = ApplicationConvert.INSTANCE.convert(DO);

        // 获取关联信息
        // 获取场景
        var scenes = applicationSceneRelMapper.selectByApplicationId(id);
        var sceneIds = new ArrayList<Long>();
        for(var scene : scenes){
            sceneIds.add(scene.getSceneId());
        }
        VO.setSceneId(sceneIds.toArray(new Long[sceneIds.size()]));

        //获取渠道
        var channels = applicationChannelRelMapper.selectByApplicationId(id);
        var channelIds = new ArrayList<Long>();
        for(var channel : channels){
            channelIds.add(channel.getChannelId());
        }
        VO.setChannelId(channelIds.toArray(new Long[channelIds.size()]));

        //根据场景ID获取数据源
        var datasources = sceneDataSourceRelMapper.selectBySceneIds(sceneIds);
        var datasourceIds = new ArrayList<Long>();
        for(var datasource : datasources){
            datasourceIds.add(datasource.getDataSourceId());
        }
        VO.setDataSourceId(datasourceIds.toArray(new Long[datasourceIds.size()]));

        return VO;
    }

    @Override
    public List<ApplicationDO> getApplicationList(Collection<Long> ids) {
        return applicationMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ApplicationRespVO> getApplicationPage(ApplicationPageDTO pageDTO) {


        var listOfDO = applicationMapper.selectPage(pageDTO);
        var listOfVO = ApplicationConvert.INSTANCE.convertPage(listOfDO);

        //获得所有 application id 供后面查询
        var applicationIds = new ArrayList<Long>();
        for(var application : listOfVO.getList()){
            applicationIds.add(application.getId());
        }

        // 获取关联信息
        // 获取 application 对应的 sceneid
        List<ApplicationSceneRelDO> scenes = null;
        if(applicationIds.size() > 0){
            scenes = applicationSceneRelMapper.selectList(
                new LambdaQueryWrapper<ApplicationSceneRelDO>()
                    .in(ApplicationSceneRelDO::getApplicationId, applicationIds)
            );
        }else{
            scenes = new ArrayList<ApplicationSceneRelDO>();
        }

        var sceneIds = new HashMap<Long, List<Long>>();
        for(var scene : scenes){
            if(sceneIds.containsKey(scene.getApplicationId())){
                sceneIds.get(scene.getApplicationId()).add(scene.getSceneId());
            }else{
                var list = new ArrayList<Long>();
                list.add(scene.getSceneId());
                sceneIds.put(scene.getApplicationId(), list);
            }
        }

        // 获取 application 对应的 channelid
        List<ApplicationChannelRelDO> channels = null;
        if(applicationIds.size() > 0){
            channels = applicationChannelRelMapper.selectList(
                new LambdaQueryWrapper<ApplicationChannelRelDO>()
                    .in(ApplicationChannelRelDO::getApplicationId, applicationIds)
            );
        }else{
            channels = new ArrayList<ApplicationChannelRelDO>();
        }

        var channelIds = new HashMap<Long, List<Long>>();
        for(var channel : channels){
            if(channelIds.containsKey(channel.getApplicationId())){
                channelIds.get(channel.getApplicationId()).add(channel.getChannelId());
            }else{
                var list = new ArrayList<Long>();
                list.add(channel.getChannelId());
                channelIds.put(channel.getApplicationId(), list);
            }
        }

        // 将获得的 scene 和 channel 填充到 VO 中
        for(var application : listOfVO.getList()){
            if(sceneIds.containsKey(application.getId())){
                application.setSceneId(sceneIds.get(application.getId()).toArray(new Long[sceneIds.get(application.getId()).size()]));
            }
            if(channelIds.containsKey(application.getId())){
                application.setChannelId(channelIds.get(application.getId()).toArray(new Long[channelIds.get(application.getId()).size()]));
            }
        }

        //datasourceid 暂时不填充

        return listOfVO;
    }

    @Override
    public Long enableApplication(Long id) {
        var application = applicationMapper.selectById(id);
        if(application == null){
            throw exception(APPLICATION_NOT_EXISTS);
        }
        application.setApplicationEnable(true);
        applicationMapper.updateById(application);
        redisService.set(String.format(APPLICATION_CACHE_KEY,application.getApplicationCode()),JSON.toJSONString(application));
        return application.getId();
        
    }

    @Override
    public Long disableApplication(Long id) {
        var application = applicationMapper.selectById(id);
        if(application == null){
            throw exception(APPLICATION_NOT_EXISTS);
        }
        application.setApplicationEnable(false);
        applicationMapper.updateById(application);
        redisService.set(String.format(APPLICATION_CACHE_KEY,application.getApplicationCode()),JSON.toJSONString(application));
        return application.getId();
    }

    @Override
    public List<IdNameVO> getKeyValue(Boolean applicationEnable) {
        var list = applicationMapper.selectList(null);
        List<IdNameVO> result = new ArrayList<>();
        for (ApplicationDO channel : list) {
            if(null!=applicationEnable ){
                if(!applicationEnable.equals(channel.getApplicationEnable())){
                    continue;
                }
                result.add(new IdNameVO(channel.getId(), channel.getApplicationName()));
            }else {
                result.add(new IdNameVO(channel.getId(), channel.getApplicationName()));
            }
        }
        return result;
    }

    @Override
    public void relateScene(Long id, String sceneIds) {
        var DO = applicationMapper.selectById(id);
        if(DO == null){
            throw exception(APPLICATION_NOT_EXISTS);
        }

        Long[] sceneIdArr = toLongArray(sceneIds, ",");

        List<SceneDO> sceneDOs = new ArrayList<>();

        if(sceneIdArr.length > 0){
            sceneDOs = sceneMapper.selectList(
                new LambdaQueryWrapper<SceneDO>()
                    .in(SceneDO::getId, (Object[]) sceneIdArr)
            );
        }

        if(sceneDOs.size() != sceneIdArr.length){
            throw exception(SCENE_NOT_EXISTS);
        }

        applicationSceneRelMapper.delete(
            new LambdaQueryWrapper<ApplicationSceneRelDO>()
                .eq(ApplicationSceneRelDO::getApplicationId, id));

        for (var secenDO : sceneDOs) {
            var rel = new ApplicationSceneRelDO();
            rel.setApplicationId(id);
            rel.setSceneId(secenDO.getId());
            applicationSceneRelMapper.insert(rel);
        }

    }

    @Override
    public void relateChannel(Long id, String channelIds) {
        var DO = applicationMapper.selectById(id);
        if(DO == null){
            throw exception(APPLICATION_NOT_EXISTS);
        }

        Long[] channelIdArr = toLongArray(channelIds, ",");

        List<ChannelDO> channelDOs = new ArrayList<>();

        if(channelIdArr.length > 0){
            channelDOs = channelMapper.selectList(
                new LambdaQueryWrapper<ChannelDO>()
                    .in(ChannelDO::getId, (Object[]) channelIdArr)
            );
        }

        if(channelDOs.size() != channelIdArr.length){
            throw exception(CHANNEL_NOT_EXISTS);
        }

        applicationChannelRelMapper.delete(
            new LambdaQueryWrapper<ApplicationChannelRelDO>()
                .eq(ApplicationChannelRelDO::getApplicationId, id));


        for (var channelDO : channelDOs) {
            var rel = new ApplicationChannelRelDO();
            rel.setApplicationId(id);
            rel.setChannelId(channelDO.getId());
            applicationChannelRelMapper.insert(rel);
        }
        

    }

    @Override
    public List<IdNameVO> getSceneKeyValue(Long id) {

        var applicationScenes = applicationSceneRelMapper.selectByApplicationId(id);

        var list = sceneMapper.selectList(null);
        List<IdNameVO> result = new ArrayList<>();
        for(var item : list){
            if(!item.getSceneEnable()) continue;
            if(!applicationScenes.stream().anyMatch(x -> x.getSceneId().equals(item.getId()))) continue;

            result.add(new IdNameVO(item.getId(), item.getSceneName()));
        }
        return result;
    
    }


}
