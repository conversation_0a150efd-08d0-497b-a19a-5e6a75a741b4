package com.organ.module.canteen.api.reconciliation.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ReconciliationPageRespVO {

    @ApiModelProperty(value = "就餐人员")
    private String dinerName;

    @ApiModelProperty(value = "开卡食堂")
    private String cardedCanteen;

    @ApiModelProperty(value = "卡类别")
    private String cardType;

    @ApiModelProperty(value = "就餐食堂")
    private String mealCanteen;

    @ApiModelProperty(value = "就餐时间")
    private LocalDateTime mealTime;

    @ApiModelProperty(value = "餐次")
    private String mealPeriod;

    @ApiModelProperty(value = "用户类别")
    private String userCategory;

    @ApiModelProperty(value = "核销员")
    private String checkerName;

    @ApiModelProperty(value = "状态")
    private String status;
}
