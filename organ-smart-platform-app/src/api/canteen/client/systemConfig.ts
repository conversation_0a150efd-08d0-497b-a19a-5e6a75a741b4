import service from "@/service";
import { ICustomerService, ISystemConfig } from "./types/systemConfig";
import { BIZ_MODULE_API_PREFIX } from "@/api/canteen/constant";

/** 模块API前缀 */
const MODULE_API_PREFIX = `${BIZ_MODULE_API_PREFIX}/systemConfig`;

/** 是否模拟数据 */
const isMock = true;

/**
 * 获取系统配置信息
 * @returns 系统配置信息
 */
export function getSystemConfig() {
  if (isMock) {
    return new Promise<ISystemConfig>((resolve) => {
      const config: ISystemConfig = {
        /** 提前预约天数配置 */
        advanceBookingDays: 7,
        /** 预约开始时间（小时） */
        bookingStartHour: 6,
        /** 预约结束时间（小时） */
        bookingEndHour: 20,
        /** 最大预约餐次数 */
        maxBookingMeals: 3,
        /** 是否允许当日预约 */
        allowSameDayBooking: true,
        /** 是否允许跨区预约 */
        allowCrossRegionBooking: true,
      };
      resolve(config);
    });
  }
  return service.get<any, ISystemConfig>(`${MODULE_API_PREFIX}/get`);
}

/**
 * 获取提前预约天数配置
 * @returns 提前预约天数
 */
export function getAdvanceBookingDays() {
  if (isMock) {
    return new Promise<number>((resolve) => {
      resolve(7); // 默认可提前7天预约
    });
  }
  return service.get<any, number>(`${MODULE_API_PREFIX}/advanceBookingDays`);
}

/** 获取客服列表 */
export function getCustomerServiceList() {
  if (isMock) {
    return new Promise<ICustomerService[]>((resolve) => {
      const list: ICustomerService[] = [
        {
          id: "1",
          name: "海政通",
          dept: "数字中心",
          phone: "6898-66666666",
        },
        {
          id: "2",
          name: "海南征信",
          dept: "数字中心",
          phone: "6898-77777777",
        },
      ];
      resolve(list);
    });
  }
  return service.get<any, ICustomerService[]>(
    `${MODULE_API_PREFIX}/customerServiceList`
  );
}
