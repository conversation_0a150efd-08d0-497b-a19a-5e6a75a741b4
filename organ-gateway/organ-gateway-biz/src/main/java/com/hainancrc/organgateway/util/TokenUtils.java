package com.hainancrc.organgateway.util;

import java.util.HashMap;
import java.util.Map;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;

import com.alibaba.fastjson.JSONObject;
import com.hainancrc.framework.common.utils.json.JsonUtils;
import com.hainancrc.framework.redis.service.RedisService;
import com.hainancrc.organgateway.dto.LoginUserDTO;
import com.hainancrc.organgateway.enums.TokenConstant;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 安全服务工具类
 */
@Slf4j
public class TokenUtils {

    public static final String AUTHORIZATION_BEARER = "Bearer";

    private static final String AUTHORIZATION_HEADER = "Authorization";

    private static final String LOGIN_USER_ID_ATTR = "loginUserId";
    private static final String LOGIN_USER_TENANT_ID = "tenantId";
    private static final String LOGIN_USER_NAME = "userName";

    private static final String LOGIN_USER_HEADER = "login-user";

    private TokenUtils() {
    }

    /**
     * 从请求中，获得认证 Token
     *
     */
    public static String obtainAuthorization(ServerWebExchange exchange) {
        String authorization = exchange.getRequest().getHeaders().getFirst(AUTHORIZATION_HEADER);

        if (!StringUtils.hasText(authorization)) {
            return null;
        }

        if (authorization.startsWith(AUTHORIZATION_BEARER)) {
            authorization = authorization.substring(7);
        }

        return authorization.trim();
    }

    public static LoginUserDTO getLoginUser(ServerWebExchange exchange, RedisService redisService,
            RedisTemplate redisTemplate) {
        String key = TokenConstant.LOGIN_TOKEN_KEY + obtainAuthorization(exchange);
        Map<String, String> loginMap = redisService.getCacheMap(key);

        log.info("用户redis:{}", JSONObject.toJSONString(loginMap));
        // Map<String,String> list =
        // (Map<String,String>)redisTemplate.opsForHash().entries("login_tokens:SYS_AUTH_1a13a109ac9649de86334a488bc3a1ed");
        // System.out.println(JSONObject.toJSONString(list));
        if (CollUtil.isEmpty(loginMap)) {
            return null;
        }
        return getLoginUser(loginMap);
    }

    /**
     * 设置登录用户
     *
     * @param exchange 请求
     * @param user     用户
     */
    public static void setLoginUser(ServerWebExchange exchange, LoginUserDTO user) {
        exchange.getAttributes().put(LOGIN_USER_ID_ATTR, user.getId());
        exchange.getAttributes().put(LOGIN_USER_TENANT_ID, user.getTenantId());
    }

    public static void main(String[] args) {
        Map<String, String> clientMap = new HashMap<>();
        // clientMap.put("user_id",123444)
    }

    /**
     * 获取系统端用户当前用户
     *
     * @return 当前用户
     */
    private static LoginUserDTO getLoginUser(Map<String, String> clientMap) {
        LoginUserDTO systemLoginUser = JSONObject.parseObject(JSONObject.toJSONString(clientMap), LoginUserDTO.class);
        return systemLoginUser;

    }

    /**
     * 将 user 并设置到 login-user 的请求头，使用 json 存储值
     *
     * @param builder 请求
     * @param user    用户
     */
    public static void setLoginUserHeader(ServerHttpRequest.Builder builder, LoginUserDTO user) {
        builder.header(LOGIN_USER_HEADER, JsonUtils.toJsonString(user));
    }
}
