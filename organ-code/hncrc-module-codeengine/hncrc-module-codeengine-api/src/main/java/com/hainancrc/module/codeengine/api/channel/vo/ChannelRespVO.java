package com.hainancrc.module.codeengine.api.channel.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import com.hainancrc.module.codeengine.api.channel.dto.*;

@ApiModel("渠道 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChannelRespVO extends ChannelBaseDTO {

    @ApiModelProperty(value = "渠道ID", required = true)
    private Integer id;

    @ApiModelProperty(value = "提交人", required = true)
    private String presenter;

    @ApiModelProperty(value = "创建时间", required = true)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", required = true)
    private Date updateTime;

}
