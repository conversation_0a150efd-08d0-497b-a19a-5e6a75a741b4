package com.hainancrc.module.codeengine.entity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 应用场景多对多 DO
 *
 * <AUTHOR>
 */
@TableName("codeengine_application_scene_rel")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationSceneRelDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 应用ID
     */
    private Long applicationId;
    /**
     * 场景ID
     */
    private Long sceneId;

}
