package com.hainancrc.module.codeengine.api.codelog.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hainancrc.module.codeengine.api.codelog.dto.*;

@ApiModel("生解码操作记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CodeLogRespVO extends CodeLogBaseDTO {

    @ApiModelProperty(value = "记录ID", required = true)
    private Long id;

    private Long[] channelIds;
    
    @ApiModelProperty(value = "渠道名称")
    private String channelName;
    
    @ApiModelProperty(value = "创建时间", required = true)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;

}
