package com.organ.module.canteen.api.booking.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.organ.module.enums.BookingStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class BookingRespVO {

    @ApiModelProperty(value = "预约单号")
    private Long id;

    @ApiModelProperty(value = "用户姓名")
    private String username;

    @ApiModelProperty(value = "联系电话")
    private String mobile;

    @ApiModelProperty(value = "所属单位")
    private String belongUnit;

    @ApiModelProperty(value = "食堂名称")
    private String canteenName;

    @ApiModelProperty(value = "餐次")
    private String meal;

    @ApiModelProperty(value = "预约时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bookingTime;

    @ApiModelProperty(value = "使用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date usageTime;

    @ApiModelProperty(value = "状态")
    private BookingStatusEnum status;

    @ApiModelProperty(value = "预约备注")
    private String remark;

    @ApiModelProperty(value = "核销员")
    private String checkInPerson;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
