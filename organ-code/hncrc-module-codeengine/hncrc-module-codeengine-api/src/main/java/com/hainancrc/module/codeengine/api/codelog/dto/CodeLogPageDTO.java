package com.hainancrc.module.codeengine.api.codelog.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.codeengine.enums.CodeLogCategory;
import com.hainancrc.module.codeengine.enums.CodeLogStatus;

import org.springframework.format.annotation.DateTimeFormat;


@ApiModel("生解码操作记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CodeLogPageDTO extends PageParam {

    @ApiModelProperty(value = "创建时间开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;

    @ApiModelProperty(value = "操作类型(apply/generate/decode)")
    private CodeLogCategory[] codeLogCategorys;

    @ApiModelProperty(value = "渠道ID")
    private Integer[] channelIds;

    @ApiModelProperty(value = "应用ID")
    private Integer[] applicationIds;

    @ApiModelProperty(value = "场景ID")
    private Integer[] sceneIds;

    @ApiModelProperty(value = "主体ID")
    private String entityId;

    @ApiModelProperty(value = "主体")
    private String entity;

    @ApiModelProperty(value = "操作状态(authRequired/fail/success)")
    private CodeLogStatus logStatus;
}
