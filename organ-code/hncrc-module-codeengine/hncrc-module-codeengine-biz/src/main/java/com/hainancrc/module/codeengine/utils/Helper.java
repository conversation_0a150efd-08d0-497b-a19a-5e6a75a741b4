package com.hainancrc.module.codeengine.utils;

import lombok.var;

public class Helper {
    public static Integer[] toIntegerArray(String str,String spliter) {
        
        var internalSpliter = ",";
        if(spliter != null){
            internalSpliter = spliter;
        }

        String[] strArray = str.split(internalSpliter);
        Integer[] intArray = new Integer[strArray.length];
        for (int i = 0; i < strArray.length; i++) {
            intArray[i] = Integer.parseInt(strArray[i]);
        }
        return intArray;
    }

    public static Long[] toLongArray(String str,String spliter) {
        
        if(str == null || "".equals(str.trim())){
            return new Long[0];
        }

        var internalSpliter = ",";
        if(spliter != null){
            internalSpliter = spliter;
        }

        String[] strArray = str.split(internalSpliter);
        Long[] intArray = new Long[strArray.length];
        for (int i = 0; i < strArray.length; i++) {
            intArray[i] = Long.parseLong(strArray[i]);
        }
        return intArray;
    }
}

