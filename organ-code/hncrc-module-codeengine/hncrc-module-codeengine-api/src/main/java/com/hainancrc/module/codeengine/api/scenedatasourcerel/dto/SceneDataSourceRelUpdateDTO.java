package com.hainancrc.module.codeengine.api.scenedatasourcerel.dto;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("场景数据源更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SceneDataSourceRelUpdateDTO extends SceneDataSourceRelBaseDTO {

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    private Integer id;

}
