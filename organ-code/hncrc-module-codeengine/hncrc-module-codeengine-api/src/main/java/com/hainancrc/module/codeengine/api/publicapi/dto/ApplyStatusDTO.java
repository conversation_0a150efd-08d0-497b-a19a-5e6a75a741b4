package com.hainancrc.module.codeengine.api.publicapi.dto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("申码记录查询 Request VO")
@Data
public class ApplyStatusDTO {

    @ApiModelProperty(value = "应用Code", required = true)
    @NotNull(message = "应用Code不能为空")
    private String application;

    @ApiModelProperty(value ="applyOrderId 订单号")
    @NotEmpty(message = "applyOrderId 订单号不能为空")
    private String applyOrderId;

}