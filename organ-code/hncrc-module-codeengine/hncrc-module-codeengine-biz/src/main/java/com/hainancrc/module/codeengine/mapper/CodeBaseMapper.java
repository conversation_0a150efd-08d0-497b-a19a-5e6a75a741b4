package com.hainancrc.module.codeengine.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ExceptionUtils;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;

public interface CodeBaseMapper<T> extends BaseMapperX<T> {

    default T selectSingle(@Param("ew") Wrapper<T> queryWrapper) {
        List<T> ts = this.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(ts)) {
           if (ts.size() != 1) {
              throw ExceptionUtils.mpe("One record is expected, but the query result is multiple records", new Object[0]);
           } else {
              return ts.get(0);
           }
        } else {
           throw ExceptionUtils.mpe("One record is expected, but the query result is empty", new Object[0]);
        }
     }
     
}
