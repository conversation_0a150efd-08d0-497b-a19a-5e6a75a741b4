package com.hainancrc.module.codeengine.api.publicapi.dto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("亮码授权验证 Request VO")
@Data
public class GenerateWithAuthDTO {

    @ApiModelProperty(value = "应用Code", required = true)
    @NotNull(message = "应用Code不能为空")
    private String application;
    
    @ApiModelProperty(value = "亮码订单号")
    @NotEmpty(message = "亮码订单号不能为空")
    private String generateOrderId;

    @ApiModelProperty(value = "授权订单号", required = false)
    private String authOrderId;

}