package com.hainancrc.module.codeengine;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;

/**
 * 代码引擎应用程序
 *
 * <AUTHOR>
 * @date 2023/09/28
 */
@EnableEurekaClient
@SpringBootApplication(scanBasePackages = {"com.hainancrc"})
public class CodeEngineApplication {
    public static void main(String[] args) {
        SpringApplication.run(CodeEngineApplication.class, args);
    }
}
