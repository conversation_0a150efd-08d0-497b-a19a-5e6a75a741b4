package com.hainancrc.organgateway.security;

import static com.hainancrc.organgateway.enums.GlobalErrorCodeConstants.LOGIN_EXPIRE;
import static com.hainancrc.organgateway.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static com.hainancrc.organgateway.enums.TokenConstant.LOGIN_USER_PERMISSIONS_KEY;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

import com.alibaba.fastjson2.JSON;
import com.hainancrc.framework.redis.service.RedisService;
import com.hainancrc.organgateway.config.IgnoreUrlsConfig;
import com.hainancrc.organgateway.dto.LoginUserDTO;
import com.hainancrc.organgateway.util.ResultUtils;
import com.hainancrc.organgateway.util.TokenUtils;
import com.hainancrc.organgateway.util.UrlsMatchUtils;
import com.hainancrc.organgateway.util.WebFrameworkUtils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import reactor.core.publisher.Mono;

/**
 * Token 过滤器，验证 token 的有效性
 * 1. 验证通过时，将 userId、userType、tenantId 通过 Header 转发给服务
 *
 */
@Component
@EnableConfigurationProperties(IgnoreUrlsConfig.class)
public class TokenAuthenticationFilter implements GlobalFilter, Ordered {

    @Resource
    private IgnoreUrlsConfig ignoreUrlsConfig;
    @Resource
    private RedisService redisService;
    @Autowired
    public RedisTemplate redisTemplate;

    private LoginUserDTO LOGIN_USER_EMPTY = new LoginUserDTO();

    @Override
    public Mono<Void> filter(final ServerWebExchange exchange, GatewayFilterChain chain) {
        String url = exchange.getRequest().getURI().getPath();
        // 获取路由
        Route route = WebFrameworkUtils.getGatewayRoute(exchange);
        // 不拦截的请求直接转发到后台
        if (ignoreUrl(route.getId(), url)) {
            return chain.filter(exchange);
        }

        String token = TokenUtils.obtainAuthorization(exchange);
        if (StrUtil.isEmpty(token)) {
            return exchange.getResponse().writeWith(ResultUtils.resultFailJSON(exchange.getResponse(), UNAUTHORIZED));
        }

        return getLoginUser(exchange, token).defaultIfEmpty(LOGIN_USER_EMPTY).flatMap(user -> {
            ServerHttpResponse response = exchange.getResponse();
            // token过期
            if (user == LOGIN_USER_EMPTY) {
                return response.writeWith(ResultUtils.resultFailJSON(response, LOGIN_EXPIRE));
            }

            // TODO 越权校验暂时不做，检验应该在业务层做
            // if(!hasPremission(url,token)) {
            // response.writeWith(ResultUtils.resultFailJSON(response,
            // LOGIN_PERMISSIONS_ERROR));
            // }
            // 设置登录用户
            TokenUtils.setLoginUser(exchange, user);
            // 将 user 并设置到 login-user 的请求头，使用 json 存储值
            ServerWebExchange newExchange = exchange.mutate()
                    .request(builder -> TokenUtils.setLoginUserHeader(builder, user)).build();
            return chain.filter(newExchange);
        });
    }

    /**
     * 是否有权限访问
     * 
     * @param url
     * @param token
     * @return
     */
    private Boolean hasPremission(String url, String token) {
        // 判断是否越权
        String permissions = redisService.getCacheMapValue(token, LOGIN_USER_PERMISSIONS_KEY);
        List<String> permissionList = JSON.parseArray(permissions, String.class);

        if (CollUtil.isEmpty(permissionList)) {
            return false;
        }

        for (String premission : permissionList) {
            if (premission.equals(url)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取登录用户信息
     * 
     * @param exchange
     * @param token
     * @return
     */
    private Mono<LoginUserDTO> getLoginUser(ServerWebExchange exchange, String token) {
        LoginUserDTO localUser = TokenUtils.getLoginUser(exchange, redisService, redisTemplate);
        if (localUser != null) {
            return Mono.just(localUser);
        }
        return Mono.empty();
    }

    /**
     * 判断是否忽略token校验
     * 
     * @param routerId
     * @param path
     * @return
     */
    private Boolean ignoreUrl(String routerId, String path) {
        List<String> urlList = ignoreUrlsConfig.getIgnoreUrls().get(routerId);
        if (CollUtil.isEmpty(urlList)) {
            return false;
        }
        return UrlsMatchUtils.matchUrl(urlList, path);
    }

    @Override
    public int getOrder() {
        return -100; // 和 Spring Security Filter 的顺序对齐
    }

}
