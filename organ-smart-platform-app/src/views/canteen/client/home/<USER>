<template>
  <div class="booking-page">
    <MobileHeader
      title="预约中心"
      bgColor="var(--color-primary)"
      textColor="#ffffff"
    />

    <div class="card-container card-gap page-content">
      <!-- 食堂信息卡片 -->
      <div class="canteen-info-card">
        <div class="canteen-details">
          <h2 class="canteen-name">
            {{ canteenInfo?.canteenName || "-" }}
          </h2>
          <p class="canteen-address">
            <i class="el-icon-location-outline"></i>
            {{ canteenInfo?.canteenAddress || "-" }}
          </p>
        </div>
      </div>

      <!-- 选择日期 -->
      <div class="section">
        <div class="section-header">
          <span class="section-title">选择日期</span>
        </div>
        <div class="date-selector">
          <div class="date-list">
            <DateCard
              v-for="date in availableDates"
              :key="date.getTime()"
              :date="date"
              :is-active="isDateSelected(date)"
              :has-selected="hasSelectedMealsForDate(date)"
              @select="handleDateSelect"
            />
          </div>
        </div>
      </div>

      <!-- 选择餐次 -->
      <div class="section">
        <div class="section-header">
          <span class="section-title">选择餐次</span>
          <span class="sub-section-title ml-10">{{
            selectedDateSubSectionTitle
          }}</span>
        </div>
        <div class="meal-list">
          <template v-for="dateKey in Object.keys(displayedMealPeriods)">
            <div v-show="dateKey === selectedDateKey" class="meal-cards">
              <MealCard
                v-for="meal in displayedMealPeriods[dateKey]"
                :key="dateKey + ':' + meal.id"
                :meal-data="meal"
                :date-key="dateKey"
                :is-selected="meal.selected"
                @select="handleMealSelect"
                @urge="handleMealUrge"
              />
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 预约信息 -->
    <div
      v-if="selectedMealPeriods.length > 0"
      class="card-container section card-gap"
    >
      <BookingCard
        :canteen-name="canteenInfo?.canteenName || ''"
        :selected-date="selectedDate"
        :selected-meals="selectedMealPeriods"
      />
    </div>

    <!-- 预约须知 -->
    <div class="card-container section card-gap">
      <BookingNotice />
    </div>

    <!-- 底部确认按钮 -->
    <div class="bottom-actions">
      <button
        class="confirm-btn"
        :class="{ disabled: totalSelectedMealsCount === 0 || submitting }"
        :disabled="totalSelectedMealsCount === 0 || submitting"
        @click="handleConfirmBooking"
      >
        <i v-if="submitting" class="el-icon-loading"></i>
        {{
          submitting
            ? "预约中..."
            : `确认预约（${totalSelectedMealsCount}个餐次）`
        }}
      </button>
    </div>

    <!-- 预约结果弹窗 -->
    <BookingResult
      v-model="showBookingResult"
      :result="bookingResult"
      :canteen-name="canteenInfo?.canteenName || ''"
      @confirm="handleBookingResultConfirm"
      @continue-booking="handleContinueBooking"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRoute } from "vue-router/composables";
import MobileHeader from "@/components/MobileHeader/index.vue";
import DateCard from "./_comp/dateCard.vue";
import MealCard from "./_comp/mealCard.vue";
import BookingCard from "./_comp/bookingCard.vue";
import BookingResult from "./_comp/bookingResult.vue";
import BookingNotice from "./_comp/bookingNotice.vue";

import { getCanteenById } from "@/api/canteen/client/canteenManage";
import { getAdvanceBookingDays } from "@/api/canteen/client/systemConfig";
import {
  getMealPeriodsByCanteenAndDate,
  submitUrgeRequest,
} from "@/api/canteen/client/mealPeriod";

import { ICanteenInfo } from "@/api/canteen/client/types/canteenManage";
import { IMealPeriod } from "@/api/canteen/client/types/mealPeriod";
import dayjs from "dayjs";
import { DATE_KEY_FORMAT, IMealPeriodVO } from "./shared/constants";
import { getDateDisplayText } from "./shared/utils";
import { useToast } from "@/components/Toast";
import { batchBooking } from "@/api/canteen/client/booking";
import { IBatchBookingResult } from "@/api/canteen/client/types/booking";
import router from "@/router";
import { BIZ_MODULE_ROUTE_PREFIX } from "@/views/canteen/configs/constants";

/** 路由 */
const route = useRoute();

/**
 * 获取食堂ID（从路由参数中获取）
 */
const canteenId = computed(() => {
  return route.query.canteenId as string;
});

/** Toast */
const toastHook = useToast();

// 响应式数据
const canteenInfo = ref<ICanteenInfo | null>(null);
/** 可预约日期列表 */
const availableDates = ref<Date[]>([]);
/** 选中的日期 */
const selectedDate = ref<Date>(new Date());
/** 餐次信息加载状态 */
const mealPeriodsLoading = ref(false);
/** 预约提交状态 */
const submitting = ref(false);
/** 催发loading */
const urgeLoading = ref(false);

// ======================= 预约提交相关 开始 =======================
/** 是否显示预约结果弹窗 */
const showBookingResult = ref(false);
/** 批量预约结果 */
const bookingResult = ref<IBatchBookingResult>({
  success: false,
  message: "",
  successMeals: [],
  failedMeals: [],
  totalCount: 0,
  successCount: 0,
  failedCount: 0,
});

/**
 * 验证预约请求
 */
const validateBookingRequest = (): { valid: boolean; message: string } => {
  if (!canteenId.value) {
    return { valid: false, message: "缺少食堂信息" };
  }

  // 检查是否有餐次被选中
  const allSelectedMeals = Object.values(displayedMealPeriods.value)
    .flat()
    .filter((meal) => meal.selected);
  if (allSelectedMeals.length === 0) {
    return { valid: false, message: "请选择至少一个餐次" };
  }

  return { valid: true, message: "" };
};

/**
 * 处理确认预约
 */
const handleConfirmBooking = async () => {
  try {
    if (submitting.value) return;
    submitting.value = true;
    const validation = validateBookingRequest();
    if (!validation.valid) {
      // 这里可以显示错误提示
      toastHook.error(validation.message, {
        // overlay: true,
        forbidClick: true,
      });
      return;
    }
    const params = {
      canteenId: canteenId.value,
      mealPeriods: Object.keys(displayedMealPeriods.value)
        .map((dateKey) => {
          return {
            bookingDate: dateKey,
            mealPeriodIds: displayedMealPeriods.value[dateKey]
              .filter((meal) => meal.selected)
              .map((meal) => meal.id),
          };
        })
        .filter((item) => item.mealPeriodIds.length > 0),
    };
    const result = await batchBooking(params);
    console.log("批量预约结果：", result);
    bookingResult.value = result;
    showBookingResult.value = true;
  } catch (error) {
    console.error("预约失败:", error);
    toastHook.error(error.msg || error.message || "系统异常，请稍后重试", {
      // overlay: true,
      forbidClick: true,
    });
  } finally {
    submitting.value = false;
  }
};

/**
 * 处理预约结果确认
 */
const handleBookingResultConfirm = (path?: string) => {
  // 可以在这里添加跳转到预约记录页面的逻辑
  if (path) {
    router.push({
      path,
    });
  } else {
    router.push({
      path: `${BIZ_MODULE_ROUTE_PREFIX}/client/diningCode`,
    });
  }
};

/**
 * 处理继续预约
 */
const handleContinueBooking = () => {
  // 关闭结果弹框
  showBookingResult.value = false;
  // 刷新一下页面数据
  initPageData();
};
// ======================= 预约提交相关 结束 =======================

// ======================= 预约日期相关 开始 =======================
/**
 * 判断日期是否被选中
 */
const isDateSelected = (date: Date): boolean => {
  const compareDate = new Date(date);
  const compareSelected = new Date(selectedDate.value);

  compareDate.setHours(0, 0, 0, 0);
  compareSelected.setHours(0, 0, 0, 0);

  return compareDate.getTime() === compareSelected.getTime();
};

/** 处理初始化可预约日期数据 */
const initAvailableDates = async (advanceDays: number) => {
  const dates: Date[] = [];
  const today = new Date();

  for (let i = 0; i < advanceDays; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    dates.push(date);
  }

  availableDates.value = dates;

  // 设置默认选中今天
  if (dates.length > 0) {
    selectedDate.value = dates[0];
  }
};

/** 选择日期后面显示的subSectionTitle */
const selectedDateSubSectionTitle = computed(() => {
  if (!selectedDate.value) return "";
  return (
    // getDateDisplayText(dayjs(selectedDate.value).add(1, "day")) +
    // ` ${dayjs(selectedDate.value).add(1, "day").format("M/D")}`
    getDateDisplayText(dayjs(selectedDate.value)) +
    ` ${dayjs(selectedDate.value).format("M/D")}`
  );
});

/**
 * 处理日期切换
 */
const handleDateSelect = async (date: Date) => {
  selectedDate.value = date;
};

/**
 * 判断日期是否有选中的餐次
 */
const hasSelectedMealsForDate = (date: Date): boolean => {
  if (!date) return false;

  const dateKey = dayjs(date).format(DATE_KEY_FORMAT);
  return (displayedMealPeriods.value[dateKey] || []).some(
    (meal) => meal.selected
  );
};
// ======================= 预约日期相关 结束 =======================

// ======================= 餐次相关 开始 =======================
/** 当前应该展示的所有餐次列表 */
const displayedMealPeriods = ref<Record<string, IMealPeriodVO[]>>({});
/** 当前选中的日期的key */
const selectedDateKey = computed(() => {
  return dayjs(selectedDate.value).format(DATE_KEY_FORMAT);
});
/** 当前选中的餐次 */
const selectedMealPeriods = computed(() => {
  return (displayedMealPeriods.value[selectedDateKey.value] || []).filter(
    (meal) => meal.selected
  );
});
/**
 * 获取餐次信息
 */
const fetchMealPeriods = async () => {
  try {
    if (!canteenId.value || mealPeriodsLoading.value) return;

    mealPeriodsLoading.value = true;
    const periods = await getMealPeriodsByCanteenAndDate({
      canteenId: canteenId.value,
    });
    initDisplayedMealPeriods(periods);
  } catch (error) {
    console.error("获取餐次信息失败:", error);
  } finally {
    mealPeriodsLoading.value = false;
  }
};

/** 初始初始化餐次数据 */
const initDisplayedMealPeriods = (mealPeriods: IMealPeriod[]) => {
  // mealPeriods 按餐次开始时间升序排列
  mealPeriods.sort((a, b) => {
    const baseDate = "2025-01-01";
    const aTime = dayjs(baseDate + " " + a.startTime);
    const bTime = dayjs(baseDate + " " + b.startTime);
    if (aTime.isBefore(bTime)) {
      return -1;
    } else if (aTime.isAfter(bTime)) {
      return 1;
    } else {
      return 0;
    }
  });
  availableDates.value.forEach((date) => {
    const dateKey = dayjs(date).format(DATE_KEY_FORMAT);
    displayedMealPeriods.value[dateKey] = [];
    mealPeriods.forEach((meal) => {
      displayedMealPeriods.value[dateKey].push({
        ...meal,
        selected: false,
      });
    });
  });
};

/**
 * 处理餐次选择
 */
const handleMealSelect = (meal: IMealPeriodVO, dateKey: string) => {
  // 更新当前日期的餐次选择状态
  displayedMealPeriods.value[dateKey] = displayedMealPeriods.value[dateKey].map(
    (m) => {
      return {
        ...m,
        selected: m.id === meal.id ? !m.selected : m.selected,
      };
    }
  );
  // 触发更新
  displayedMealPeriods.value = { ...displayedMealPeriods.value };
};

/**
 * 处理餐次催发
 */
const handleMealUrge = async (meal: IMealPeriod, dateKey: string) => {
  try {
    if (urgeLoading.value) return;
    urgeLoading.value = true;
    const urgeRequest = {
      canteenId: canteenId.value,
      mealPeriodId: meal.id,
      bookingDate: dateKey,
      reason: "用户主动催发",
    };

    const result = await submitUrgeRequest(urgeRequest);

    if (result) {
      // 更新当前日期餐次状态 displayedMealPeriods
      displayedMealPeriods.value[dateKey] = displayedMealPeriods.value[
        dateKey
      ].map((m) => {
        return {
          ...m,
          isUrged: m.id === meal.id ? true : m.isUrged,
          urgeCount: m.id === meal.id ? m.urgeCount + 1 : m.urgeCount,
        };
      });
      displayedMealPeriods.value = { ...displayedMealPeriods.value };

      toastHook.success("已成功提交催发请求，请耐心等待食堂管理人员处理", {
        overlay: true,
        forbidClick: true,
      });
    }
  } catch (error) {
    toastHook.error(error.msg || "催发请求失败，请稍后重试", {
      overlay: true,
      forbidClick: true,
    });
  } finally {
    urgeLoading.value = false;
  }
};

/**
 * 获取所有日期的已选餐次总数
 */
const totalSelectedMealsCount = computed(() => {
  return Object.values(displayedMealPeriods.value)
    .flat()
    .filter((meal) => meal.selected).length;
});
// ======================= 餐次相关 结束 =======================
/** 初始化数据加载状态 */
const loadingData = ref(false);
/**
 * 初始化页面数据
 */
const initPageData = async () => {
  try {
    if (loadingData.value) return;
    loadingData.value = true;
    const [availableDays, mealPeriods, canteenInfoData] = await Promise.all([
      getAdvanceBookingDays(),
      getMealPeriodsByCanteenAndDate({ canteenId: canteenId.value }),
      getCanteenById(canteenId.value),
    ]);

    // 初始化可预约日期天数需要在初始化餐次数据之前
    initAvailableDates(availableDays);
    initDisplayedMealPeriods(mealPeriods);
    canteenInfo.value = canteenInfoData;

    // 获取默认日期的餐次信息
    if (selectedDate.value) {
      await fetchMealPeriods();
    }
  } catch (error) {
    console.log("初始化页面数据失败:", error);
  } finally {
    loadingData.value = false;
  }
};

/** loading状态 */
const loading = computed(() => {
  return (
    loadingData.value ||
    mealPeriodsLoading.value ||
    submitting.value ||
    urgeLoading.value
  );
});

watch(
  () => loading.value,
  (val) => {
    if (val) {
      toastHook.loading("", {
        overlay: true,
        forbidClick: true,
        duration: 0,
      });
    } else {
      toastHook.clear();
    }
  }
);

// 页面挂载时初始化数据
onMounted(() => {
  initPageData();
});
</script>

<style scoped lang="scss">
.booking-page {
  min-height: 100vh;
  background: linear-gradient(180deg, var(--color-primary) 0%, #f5f5f5 50%);
  padding: 0 12px;
  padding-bottom: 100px; // 为底部按钮留出空间
  // 为顶部留出安全区域
  padding-top: calc(var(--header-height) + env(safe-area-inset-top));

  .card-container {
    width: 100%;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #ffffff;
    overflow: hidden;
  }

  .page-content {
    margin-top: 10px;

    .canteen-info-card {
      padding: 20px 15px 30px;
      border-radius: 4px 0;
      display: flex;
      align-items: center;
      background-image: url("@/assets/images/mobile/canteen.png");
      background-position: right 20px;
      background-repeat: no-repeat;
      background-size: auto 100%;
      background-color: #e6f3ff;
      height: 100px;
      overflow: hidden;

      .canteen-details {
        flex: 1;
        max-width: 50%;

        .canteen-name {
          font-size: 18px;
          font-weight: 500;
          color: #1d1e20;
          margin: 0 0 8px 0;
          line-height: 1.2;
        }

        .canteen-address {
          font-size: 13px;
          color: #808080;
          margin: 0;
          display: flex;
          align-items: stretch;
          line-height: 1.4;
          // 超出换行
          word-wrap: break-word;
          overflow: hidden;

          i {
            font-size: 13px;
            line-height: 1.4;
          }
        }
      }
    }

    .date-selector {
      .date-list {
        display: flex;
        overflow-x: auto;
        gap: 8px;

        &::-webkit-scrollbar {
          display: none;
        }
      }
    }

    .meal-list {
      .loading-state,
      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        color: #999999;

        i {
          font-size: 32px;
          margin-bottom: 8px;
        }

        span {
          font-size: 14px;
        }
      }

      .loading-state i {
        animation: rotate 1s linear infinite;
      }

      .meal-cards {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }
    }
  }

  .section {
    padding: 10px;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .section-title {
        font-size: 16px;
        color: #383838;
      }

      .sub-section-title {
        font-size: 12px;
        color: #808080;
      }
    }
  }

  .ml-10 {
    margin-left: 10px;
  }

  .card-gap {
    margin-bottom: 10px;
  }

  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    border-top: 1px solid #e5e5e5;
    z-index: 100;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);

    .confirm-btn {
      width: 100%;
      height: 65px;
      background: var(--color-primary);
      color: #ffffff;
      border: none;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      box-shadow: 0 4px 16px rgba(58, 136, 245, 0.3);

      &:hover:not(.disabled) {
        opacity: 0.8;
        // background: linear-gradient(135deg, #2c7ae0 0%, #5a6fd8 100%);
        box-shadow: 0 6px 20px rgba(58, 136, 245, 0.4);
      }

      &.disabled {
        // background: #cccccc;
        background: var(--color-primary);
        opacity: 0.6;
        cursor: not-allowed;
        box-shadow: none;
        transform: none;
      }

      i {
        animation: rotate 1s linear infinite;
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
