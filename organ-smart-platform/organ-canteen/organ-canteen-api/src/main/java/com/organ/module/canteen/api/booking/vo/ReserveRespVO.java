package com.organ.module.canteen.api.booking.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 预约返回结果
 */
@Data
@ApiModel("预约返回结果")
public class ReserveRespVO {

    @ApiModelProperty(value = "食堂id")
    private Long canteenId;

    @ApiModelProperty(value = "食堂名称")
    private String canteenName;

    @ApiModelProperty(value = "预约成功数量")
    private Integer successCount = 0;

    @ApiModelProperty(value = "预约失败数量")
    private Integer failedCount = 0;

    @ApiModelProperty(value = "预约失败信息")
    private List<ReserveFailedRespVO> failedReserveList = new ArrayList<>();

    public void incrementSuccessCount() {
        this.successCount += 1;
    }

    public void incrementFailedCount() {
        this.failedCount += 1;
    }

    public void addFailedReserve(ReserveFailedRespVO reserveFailedRespVO) {
        this.failedReserveList.add(reserveFailedRespVO);
    }

}
