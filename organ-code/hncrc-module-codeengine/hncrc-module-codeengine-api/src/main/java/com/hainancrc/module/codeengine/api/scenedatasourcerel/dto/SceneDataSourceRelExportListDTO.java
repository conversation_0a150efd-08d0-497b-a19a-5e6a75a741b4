package  com.hainancrc.module.codeengine.api.scenedatasourcerel.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;


@ApiModel(value = "场景数据源 Excel 导出 Request VO", description = "参数和 SceneDataSourceRelPageReqVO 是一致的")
@Data
public class SceneDataSourceRelExportListDTO {

    @ApiModelProperty(value = "场景ID")
    private Integer sceneId;
    @ApiModelProperty(value = "数据源ID")
    private Integer dataSourceId;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
