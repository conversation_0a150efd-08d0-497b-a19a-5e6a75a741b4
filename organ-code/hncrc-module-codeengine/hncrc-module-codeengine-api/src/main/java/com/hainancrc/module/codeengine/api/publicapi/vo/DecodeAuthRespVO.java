package com.hainancrc.module.codeengine.api.publicapi.vo;

import com.hainancrc.module.codeengine.enums.CodeLogStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("DecodeAuthRespVO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DecodeAuthRespVO {

    @ApiModelProperty(value = "解码码状态(authRequired/fail/success)", required = true)
    private CodeLogStatus decodeStatus;

}