package com.hainancrc.module.codeengine.mapper.applicationchannelrel;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.module.codeengine.entity.ApplicationChannelRelDO;
import org.apache.ibatis.annotations.Mapper;
import com.hainancrc.module.codeengine.api.applicationchannelrel.dto.*;

/**
 * 应用渠道多对多 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ApplicationChannelRelMapper extends BaseMapperX<ApplicationChannelRelDO> {

    default PageResult<ApplicationChannelRelDO> selectPage(ApplicationChannelRelPageDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<ApplicationChannelRelDO>()
                .eqIfPresent(ApplicationChannelRelDO::getApplicationId, reqDTO.getApplicationId())
                .eqIfPresent(ApplicationChannelRelDO::getChannelId, reqDTO.getChannelId())
                .orderByDesc(ApplicationChannelRelDO::getId));
    }

    default List<ApplicationChannelRelDO> selectList(ApplicationChannelRelExportListDTO reqDTO) {
        return selectList(new LambdaQueryWrapperX<ApplicationChannelRelDO>()
                .eqIfPresent(ApplicationChannelRelDO::getApplicationId, reqDTO.getApplicationId())
                .eqIfPresent(ApplicationChannelRelDO::getChannelId, reqDTO.getChannelId())
                .orderByDesc(ApplicationChannelRelDO::getId));
    }

    default List<ApplicationChannelRelDO> selectByApplicationId(Long id){
        return selectList(new LambdaQueryWrapperX<ApplicationChannelRelDO>()
                .eqIfPresent(ApplicationChannelRelDO::getApplicationId, id)
                .orderByDesc(ApplicationChannelRelDO::getId));
    }

}
