package com.hainancrc.module.codeengine.service.publicapi;

import javax.annotation.Resource;
import javax.validation.Valid;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.framework.redis.service.RedisService;
import com.hainancrc.framework.security.ClientLoginUser;
import com.hainancrc.framework.security.core.util.TokenUtils;
import com.hainancrc.module.codeengine.entity.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.authorization.api.authorizationcenter.AuthorizationCenterApi;
import com.hainancrc.module.codeengine.api.common.KeyNameVO;
import com.hainancrc.module.codeengine.api.publicapi.dto.ApplyCreateDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.ApplyStatusDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.ApplyWithAuthDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.DecodeCreateDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.DecodeStatusDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.DecodeWithAuthDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.GenerateCreateDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.GenerateStatusDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.GenerateWithAuthDTO;
import com.hainancrc.module.codeengine.api.publicapi.vo.ApplyCreateRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.ApplyStatusRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.DecodeAuthRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.DecodeCreateRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.DecodeStatusRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.GenerateAuthRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.GenerateCreateRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.GenerateStatusRespVO;
import com.hainancrc.module.codeengine.configuration.AppConfiguration;
import com.hainancrc.module.codeengine.enums.CodeLogStatus;
import com.hainancrc.module.codeengine.enums.ApplyCategory;
import com.hainancrc.module.codeengine.enums.CodeLogCategory;
import com.hainancrc.module.codeengine.mapper.application.ApplicationMapper;
import com.hainancrc.module.codeengine.mapper.applicationscenerel.ApplicationSceneRelMapper;
import com.hainancrc.module.codeengine.mapper.apply.ApplyMapper;
import com.hainancrc.module.codeengine.mapper.applychannelrel.ApplyChannelRelMapper;
import com.hainancrc.module.codeengine.mapper.channel.ChannelMapper;
import com.hainancrc.module.codeengine.mapper.codelog.CodeLogMapper;
import com.hainancrc.module.codeengine.mapper.coderecord.CodeRecordMapper;
import com.hainancrc.module.codeengine.mapper.scene.SceneMapper;
import com.hainancrc.module.codeengine.mapper.scenedatasourcerel.SceneDataSourceRelMapper;
import com.hainancrc.module.codeengine.service.common.CodeService;
import com.hainancrc.module.codeengine.service.datasource.DataSourceService;

import cn.hutool.json.JSONObject;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.*;

import java.util.*;

import lombok.extern.slf4j.Slf4j;

//TODO 涉及到查询 CodeLog 表的操作，需要优化
@Service
@Validated
@Slf4j
public class PublicApiServiceImpl implements PublicApiService {

    @Resource
    private AuthorizationCenterApi authorizationCenterApi;

    @Resource
    private ApplyMapper applyMapper;

    @Resource
    private ApplyChannelRelMapper applyChannelRelMapper;

    @Resource
    private CodeLogMapper codeLogMapper;

    @Resource
    private ApplicationMapper applicationMapper;

    @Resource
    private ApplicationSceneRelMapper applicationSceneRelMapper;

    @Resource
    private SceneMapper sceneMapper;

    @Resource
    private SceneDataSourceRelMapper sceneDataSourceRelMapper;

    @Resource
    private ChannelMapper channelMapper;

    @Resource
    private CodeRecordMapper codeRecordMapper;

    @Resource
    private CodeService codeService;

    @Resource
    private DataSourceService dataSourceService;

    @Resource
    private AppConfiguration appConfiguration;

    @Resource
    private RedisService redisService;


    private CodeLogDO createLog(
            ApplyDO applyDO,SceneDO sceneDO,
            Long[] channelIds, String channelCode,
            CodeLogCategory category,
            CodeLogStatus status,
            String codeKey,
            String requestEntity,
            String requestEntityId,
            ApplyCategory codeCategory) {



        CodeLogDO logDO = new CodeLogDO();

        logDO.setCodeLogCategory(category);

        if (category == CodeLogCategory.APPLY) {
            logDO.setCodeLogAuthId(sceneDO.getApplyAuthId());
        } else if (category == CodeLogCategory.GENERATE) {
            logDO.setCodeLogAuthId(sceneDO.getGenerateAuthId());
        } else if (category == CodeLogCategory.DECODE) {
            logDO.setCodeLogAuthId(sceneDO.getDecodeAuthId());
        }
        ClientLoginUser clientLoginUser = TokenUtils.getClientLoginUser(redisService);
        if (Objects.nonNull(clientLoginUser)){
            logDO.setCreateName(clientLoginUser.getRealName());
            logDO.setCreator(clientLoginUser.getUserAccount());
        }
        long id = IdUtil.getSnowflakeNextId();
        logDO.setId(id);
        logDO.setChannelIdStr(codeService.comboChannelIdToStr(channelIds));
        logDO.setChannelCode(channelCode);
        logDO.setCodeLogApplyId(applyDO.getId());
        logDO.setCodeKey(codeKey);
        logDO.setLogStatus(status);
        logDO.setApplicationId(applyDO.getApplicationId());
        logDO.setSceneId(applyDO.getSceneId());
        logDO.setEntityId(applyDO.getEntityId());
        logDO.setEntity(applyDO.getEntity());
        logDO.setRequestEntity(requestEntity);
        logDO.setRequestEntityId(requestEntityId);
        logDO.setValidityTime(applyDO.getExpiredTime());
        logDO.setCodeCategory(codeCategory);
        logDO.setCodeLogOrderId("NO" + id);
        codeLogMapper.insert(logDO);
        return logDO;
    }


    private CodeLogStatus getStatusByAuthId(String authId) {
        if (StringUtils.isBlank(authId)) {
            return CodeLogStatus.SUCCESS;
        } else {
            return CodeLogStatus.AUTH_REQUIRED;
        }
    }

    private CodeLogDO getLogDOAuthWith(String applicationCode, String codeLogOrderId, String authOrderId,CodeLogCategory category) {

        ApplicationDO applicationDO = getApplication(applicationCode);

        CodeLogDO logDO = codeLogMapper.selectByOrderId(codeLogOrderId);
        if (logDO == null || logDO.getCodeLogCategory() != category) {
            throw exception(CODE_LOG_NOT_EXISTS);
        }

        if (logDO.getApplicationId() != applicationDO.getId()) {
            throw exception(APPLICATION_NOT_AUTH);
        }


        if(StringUtils.isNotEmpty(authOrderId)){
            //授权订单为空，不查询授权中心
            Integer authOrderIdInt = Integer.parseInt(authOrderId);
            CommonResult<String> result = authorizationCenterApi.getUser(authOrderIdInt);

            // check
            // hncrc-module-authorization-api\src\main\java\com\hainancrc\module\authorization\enums\AuthStatusConstant.java
            if ("3".equals(result.getData())) { // public static final String FINISHED = "3";
                logDO.setLogStatus(CodeLogStatus.SUCCESS);
            } else if(!("1".equals(result.getData()))) { //public static final String ONGOING = "1";
                logDO.setLogStatus(CodeLogStatus.FAIL);
            }
        }else {
            logDO.setLogStatus(CodeLogStatus.SUCCESS);
        }


        
        codeLogMapper.updateById(logDO);
        return logDO;
    }

    // TODO 需要将 public api 和 后台生码功能的代码合并
    @Override
    public ApplyCreateRespVO createApply(@Valid ApplyCreateDTO createDTO) {

        ApplicationDO applicationDO = getApplication(createDTO.getApplication());

        if(!applicationDO.getApplicationEnable()){
            throw exception(APPLICATION_DISABLED);
        }

        ChannelDO channelDO = getChannelDO(createDTO.getChannel());

        SceneDO sceneDO = sceneMapper.selectByCode(createDTO.getScene());
        if (sceneDO == null) {
            throw exception(SCENE_NOT_EXISTS);
        }
        if(!sceneDO.getSceneEnable()){
            throw exception(SCENE_DISABLED);
        }
        // 检查每个 channel 是否有跟 application 关联
        if (!codeService.applicationRelatedChannel(applicationDO.getId(), channelDO.getId())) {
            throw exception(APPLICATION_CHANNEL_REL_NOT_EXISTS);
        }

        // 检查每个 scene 是否有跟 application 关联
        if (!codeService.applicationRelatedScene(applicationDO.getId(), sceneDO.getId())) {
            throw exception(APPLICATION_SCENE_REL_NOT_EXISTS);
        }

        ApplyDO applyDO = new ApplyDO();
        applyDO.setGeneratedCount(0);
        applyDO.setApplicationId(applicationDO.getId());
        applyDO.setSceneId(sceneDO.getId());
        applyDO.setEntityId(createDTO.getEntityId());
        applyDO.setEntity(createDTO.getEntity());
        applyDO.setExpiredTime(createDTO.getExpiredTime());
        applyDO.setApplyDescription(createDTO.getDescription());

        applyMapper.insert(applyDO);

        ApplyChannelRelDO applyChannelRelDO = new ApplyChannelRelDO();
        applyChannelRelDO.setApplyId(applyDO.getId());
        applyChannelRelDO.setChannelId(channelDO.getId());
        applyChannelRelMapper.insert(applyChannelRelDO);

        CodeLogStatus logStatus = getStatusByAuthId(sceneDO.getApplyAuthId());

        Long[] channelIds = new Long[] { channelDO.getId() };
        CodeLogDO logDO = createLog(
                applyDO,sceneDO,
                channelIds,
                channelDO.getChannelCode(),
                CodeLogCategory.APPLY,
                logStatus,
                null, null, null,null);
        ApplyCreateRespVO resp = new ApplyCreateRespVO();
        resp.setApplyOrderId(logDO.getCodeLogOrderId());
        resp.setApplyStatus(logDO.getLogStatus());
        resp.setAuthActionId(sceneDO.getApplyAuthId());
        return resp;
    }

    @Override
    public ApplyStatusRespVO applyWithAuth(@Valid ApplyWithAuthDTO authDTO) {
        CodeLogDO logDO = getLogDOAuthWith(authDTO.getApplication(), authDTO.getApplyOrderId(), authDTO.getAuthOrderId(), CodeLogCategory.APPLY);
        ApplyStatusRespVO resp = new ApplyStatusRespVO();
        resp.setApplyStatus(logDO.getLogStatus());
        return resp;

    }

    @Override
    public GenerateCreateRespVO createGenerate(@Valid GenerateCreateDTO createDTO) {

        CodeLogDO applyLogDO = codeLogMapper.selectByOrderId(createDTO.getApplyOrderId());
        ApplyDO applyDO = applyMapper.selectById(applyLogDO.getCodeLogApplyId());
        ApplicationDO applicationDO = applicationMapper.selectById(applyDO.getApplicationId());

        if(applicationDO == null){
            throw exception(APPLICATION_NOT_EXISTS);
        }
        if(!applicationDO.getApplicationEnable()){
            throw exception(APPLICATION_DISABLED);
        }

        SceneDO sceneDO = sceneMapper.selectById(applyDO.getSceneId());
        if(sceneDO == null){
            throw exception(SCENE_NOT_EXISTS);
        }
        if(!sceneDO.getSceneEnable()){
            throw exception(SCENE_DISABLED);
        }

        if (applyLogDO == null || applyDO == null) {
            throw exception(CODE_LOG_NOT_EXISTS);
        }

        if (applyLogDO.getLogStatus() != CodeLogStatus.SUCCESS) {
            throw exception(APPLY_STATUS_NOT_SUCCESS);
        }

        ChannelDO channelDO = channelMapper.selectByCode(createDTO.getChannel());
        if (channelDO == null) {
            throw exception(CHANNEL_NOT_EXISTS);
        }

        if (applyDO.getExpiredTime() != null && ! codeService.validateExpired(sceneDO, createDTO.getCategory(), applyDO.getExpiredTime())) {
            throw exception(SCENE_EXPIRED_TIME_ERROR);
        }

        CodeLogStatus codeLogStatus = getStatusByAuthId(sceneDO.getGenerateAuthId());

        applyDO.setGeneratedCount(applyDO.getGeneratedCount() + 1);
        applyMapper.updateById(applyDO);

        CodeLogDO logDO = createLog(
                applyDO,sceneDO,
                new Long[] { channelDO.getId() }, channelDO.getChannelCode(),
                CodeLogCategory.GENERATE,
                codeLogStatus,
                null, null, null, createDTO.getCategory());

        GenerateCreateRespVO respVO = new GenerateCreateRespVO();
        respVO.setGenerateOrderId(logDO.getCodeLogOrderId());
        respVO.setGenerateStatus(logDO.getLogStatus());
        respVO.setAuthActionId(logDO.getCodeLogAuthId());

        return respVO;
    }

    @Override
    public ApplyStatusRespVO applyStatus(@Valid ApplyStatusDTO statusDTO) {

        ApplicationDO applicationDO = applicationMapper.selectByCode(statusDTO.getApplication());
        if (applicationDO == null) {
            throw exception(APPLICATION_NOT_EXISTS);
        }

        CodeLogDO logDO = codeLogMapper.selectByOrderId(statusDTO.getApplyOrderId());
        if (logDO == null || logDO.getCodeLogCategory() != CodeLogCategory.APPLY) {
            throw exception(CODE_LOG_NOT_EXISTS);
        }

        if (logDO.getApplicationId() != applicationDO.getId()) {
            throw exception(APPLICATION_NOT_AUTH);
        }

        ApplyStatusRespVO resp = new ApplyStatusRespVO();
        resp.setApplyStatus(logDO.getLogStatus());
        return resp;

    }

    @Override
    public GenerateAuthRespVO generateWithAuth(@Valid GenerateWithAuthDTO authDTO) {
        CodeLogDO logDO = getLogDOAuthWith(authDTO.getApplication(), authDTO.getGenerateOrderId(), authDTO.getAuthOrderId(), CodeLogCategory.GENERATE);
       return new GenerateAuthRespVO(logDO.getLogStatus());
    }

    @Override
    public GenerateStatusRespVO generateStatus(@Valid GenerateStatusDTO statusDTO) {

        ApplicationDO applicationDO = applicationMapper.selectByCode(statusDTO.getApplication());
        if (applicationDO == null) {
            throw exception(APPLICATION_NOT_EXISTS);
        }

        CodeLogDO logDO = codeLogMapper.selectByOrderId(statusDTO.getGenerateOrderId());
        if (logDO == null || logDO.getCodeLogCategory() != CodeLogCategory.GENERATE) {
            throw exception(CODE_LOG_NOT_EXISTS);
        }

        if (logDO.getApplicationId() != applicationDO.getId()) {
            throw exception(APPLICATION_NOT_AUTH);
        }

        if (logDO.getLogStatus() != CodeLogStatus.SUCCESS) {
            GenerateStatusRespVO respVO = new GenerateStatusRespVO();
            respVO.setGenerateStatus(logDO.getLogStatus());
            return respVO;

        } else {
            ApplyDO applyDO = applyMapper.selectById(logDO.getCodeLogApplyId());
            // TODO 并发问题
            if (logDO.getCodeKey() == null) {
                CodeRecordDO codeRecordDO = codeService.createCodeRecord(applyDO, applicationDO.getApplicationCodePrefix(),statusDTO.getCategory());
                logDO.setCodeKey(codeRecordDO.getCodeKey());
                codeLogMapper.updateById(logDO);
            }
            SceneDO sceneDO = sceneMapper.selectById(logDO.getSceneId());
            CodeRecordDO codeDO = codeRecordMapper.selectById(logDO.getCodeKey());
            GenerateStatusRespVO respVO = new GenerateStatusRespVO();
            respVO.setGenerateStatus(logDO.getLogStatus());
            respVO.setCodeUrl(codeDO.getCodeUrl());

            // 给出过期时间
            if (statusDTO.getCategory() == ApplyCategory.STATIC) {
                if (applyDO.getExpiredTime() != null) {
                    respVO.setExpiredTime(applyDO.getExpiredTime());
                } else {
                    respVO.setExpiredTime(
                            codeService.sceneExpiredTime(sceneDO, statusDTO.getCategory(), codeDO.getCreateTime()));
                }
            } else if (sceneDO.getDynamicExpiredMinute() != null && statusDTO.getCategory() == ApplyCategory.DYNAMIC) {
                respVO.setExpiredTime(
                        codeService.sceneExpiredTime(sceneDO, statusDTO.getCategory(), codeDO.getCreateTime()));
            } else {
                respVO.setExpiredTime(null);
            }
            respVO.setExpiredTimeStamp(respVO.getExpiredTime());

            return respVO;

        }
    }

    private static final String APPLICATION_CACHE_KEY = "code:engine:application:%s";
    private static final String CHANNEL_CACHE_KEY = "code:engine:channel:%s";
    private static final String SCENE_CACHE_KEY = "code:engine:scene:%s";

    @Override
    public DecodeCreateRespVO createDecode(@Valid DecodeCreateDTO createDTO) {

        getApplication(createDTO.getApplication());
        String q = null;
        try {
            String qrcodeUrl = createDTO.getCodeUrl().replace("&amp;", "&"); //TODO 不知道为何 & 会被自动转义，手工先转义回来
            String lengthStr = qrcodeUrl.substring(qrcodeUrl.lastIndexOf("&l=") + 3);
            Integer length = Integer.parseInt(lengthStr);
            String codeString = createDTO.getCodeUrl().substring(length);
            String codeKey = codeString.substring(0, codeString.indexOf("&"));
            q = codeKey;
        } catch (Exception ex) {
            throw exception(CODE_READ_ERROR);

        }

        CodeRecordDO codeRecordDO = codeRecordMapper.selectById(q);
        if (codeRecordDO == null) {
            throw exception(APPLICATION_NOT_AUTH);
        }

        ApplyDO applyDO = applyMapper.selectById(codeRecordDO.getApplyId());

        ChannelDO channelDO = getChannelDO(createDTO.getChannel());

        boolean existApplyChannel = applyChannelRelMapper.exists(
                new LambdaQueryWrapperX<ApplyChannelRelDO>()
                        .eq(ApplyChannelRelDO::getApplyId, applyDO.getId()));
        if (!existApplyChannel) {
            throw exception(APPLY_CHANNEL_REL_NOT_EXISTS);
        }

        SceneDO sceneDO = getSceneDO(applyDO.getSceneId());

        CodeLogStatus codeLogStatus = getStatusByAuthId(sceneDO.getDecodeAuthId());
        if(codeRecordDO.getApplyCategory().equals(ApplyCategory.STATIC)) {
            //静态码不需要授权
            codeLogStatus = CodeLogStatus.SUCCESS;
        }

        CodeLogDO codeLog = createLog(
                applyDO,sceneDO,
                new Long[] { channelDO.getId() }, channelDO.getChannelCode(),
                CodeLogCategory.DECODE,
                codeLogStatus,
                codeRecordDO.getCodeKey(),
                createDTO.getRequestEntity(),
                createDTO.getRequestEntityId(),
                codeRecordDO.getApplyCategory());

        DecodeCreateRespVO VO = new DecodeCreateRespVO();
        VO.setDecodeOrderId(codeLog.getCodeLogOrderId());
        VO.setDecodeStatus(codeLog.getLogStatus());
        VO.setAuthActionId(codeLog.getCodeLogAuthId());
        return VO;

    }

    @Override
    public DecodeAuthRespVO decodeWithAuth(@Valid DecodeWithAuthDTO authDTO) {
        CodeLogDO logDO = getLogDOAuthWith(authDTO.getApplication(), authDTO.getDecodeOrderId(), authDTO.getAuthOrderId(), CodeLogCategory.DECODE);
        return new DecodeAuthRespVO(logDO.getLogStatus());
    }

    @Override
    public DecodeStatusRespVO decodeStatus(@Valid DecodeStatusDTO statusDTO) {

        ApplicationDO applicationDO = getApplication(statusDTO.getApplication());

        CodeLogDO logDO = codeLogMapper.selectByOrderId(statusDTO.getDecodeOrderId());
        if (logDO == null || logDO.getCodeLogCategory() != CodeLogCategory.DECODE) {
            throw exception(CODE_LOG_NOT_EXISTS);
        }

        if (logDO.getApplicationId() != applicationDO.getId()) {
            throw exception(APPLICATION_NOT_AUTH);
        }

        SceneDO sceneDO = getSceneDO(logDO.getSceneId());
        if (logDO.getLogStatus() != CodeLogStatus.SUCCESS) {

            DecodeStatusRespVO VO = new DecodeStatusRespVO();
            VO.setDecodeStatus(logDO.getLogStatus());
            return VO;

        } else {

            ApplyDO applyDO = applyMapper.selectById(logDO.getCodeLogApplyId());
            List<ApplyChannelRelDO> applyChannelRelsDO = applyChannelRelMapper.selectList(
                    new LambdaQueryWrapperX<ApplyChannelRelDO>().eq(ApplyChannelRelDO::getApplyId, applyDO.getId()));

            ArrayList<String> listOfChannelCode = new ArrayList<>();
            for (ApplyChannelRelDO item : applyChannelRelsDO) {
                ChannelDO channelDO = channelMapper.selectById(item.getChannelId());
                listOfChannelCode.add(channelDO.getChannelCode());
            }

            CodeRecordDO codeRecordDO = codeRecordMapper.selectById(logDO.getCodeKey());
            codeRecordDO.setScanCount(codeRecordDO.getScanCount() + 1);
            codeRecordMapper.updateById(codeRecordDO);

            DecodeStatusRespVO VO = new DecodeStatusRespVO();
            VO.setCodeCategory(codeRecordDO.getApplyCategory());
            VO.setChannelCodes(listOfChannelCode.toArray(new String[0]));
            VO.setEntity(logDO.getEntity());
            VO.setEntityId(logDO.getEntityId());
            VO.setSceneCode(sceneDO.getSceneCode());
            VO.setSceneName(sceneDO.getSceneName());
            VO.setDecodeStatus(logDO.getLogStatus());
            //返回场景的额外参数
            VO.setExtraParameters(sceneDO.getExtraParameters());
            
            if(applyDO.getExpiredTime() != null){
                VO.setExpiredTime(applyDO.getExpiredTime());
            }else{
                VO.setExpiredTime(codeService.sceneExpiredTime(sceneDO, codeRecordDO.getApplyCategory(), codeRecordDO.getCreateTime()));
            }

            Date currentDate = new Date();
            if(codeRecordDO.getCodeRecordExpired()){
                VO.setExpired(true);
            } else if(VO.getExpiredTime() != null && VO.getExpiredTime().before(currentDate)){
                VO.setExpired(true);
            }else if(codeRecordDO.getApplyCategory() == ApplyCategory.DYNAMIC &&
                    sceneDO.getDynamicExpiredTimes() != null &&
                    codeRecordDO.getScanCount() > sceneDO.getDynamicExpiredTimes()){
                VO.setExpired(true);
            }else{
                VO.setExpired(false);
            }
            if(codeRecordDO.getApplyCategory() == ApplyCategory.DYNAMIC
              && sceneDO.getDynamicExpiredTimes() != null){
                VO.setRemainingTimes(sceneDO.getDynamicExpiredTimes()-codeRecordDO.getScanCount());
            }else {
                //不限制
                VO.setRemainingTimes(-1);
            }

            if (!VO.getExpired()) {

                List<SceneDataSourceRelDO> dataSourceDOs = sceneDataSourceRelMapper.selectList(
                        new LambdaQueryWrapperX<SceneDataSourceRelDO>()
                                .eq(SceneDataSourceRelDO::getSceneId, sceneDO.getId()));

                HashMap<String, Object> datas = new HashMap<>();

                for (SceneDataSourceRelDO item : dataSourceDOs) {

                    DataSourceDO dataSourceDO = dataSourceService.getDataSource(item.getDataSourceId());

                    try {
                        String response = dataSourceService.requestDataSource(item.getDataSourceId());
                        log.info("datasource:%d, response:%s", dataSourceDO.getId(), response);
                        JSONObject jObject = new JSONObject(response);
                        datas.put(dataSourceDO.getDataSourceCode(), (Object) jObject);

                    } catch (ServiceException ex) {

                        log.debug("code:%s, message:%s",ex.getCode(),ex.getMessage());
                        datas.put("code", ex.getCode());
                        datas.put("data", null);
                        datas.put("msg", ex.getMessage());
                    }
                }
                VO.setDataSourceData(datas);
            }
            return VO;
        }
    }

    @Override
    public List<KeyNameVO> sceneList(String application) {

        ApplicationDO applicationDO = applicationMapper.selectByCode(application);

        if (applicationDO == null) {
            throw exception(APPLICATION_NOT_EXISTS);
        }
        if(!applicationDO.getApplicationEnable()){
            throw exception(APPLICATION_DISABLED);
        }

        List<ApplicationSceneRelDO> list = applicationSceneRelMapper.selectByApplicationId(applicationDO.getId());
        List<KeyNameVO> result = new ArrayList<>();
        for (ApplicationSceneRelDO item : list) {
            SceneDO sceneDO = sceneMapper.selectById(item.getSceneId());
            if(!sceneDO.getSceneEnable()) continue;
            result.add(new KeyNameVO(sceneDO.getSceneCode(), sceneDO.getSceneName()));
        }
        return result;
    }

    @Override
    public String getSceneAuthId(String application,
                                 String scene,
                                 CodeLogCategory category,
                                 ApplyCategory applyCategory) {

        ApplicationDO applicationDO = getApplication(application);

        if(!applicationDO.getApplicationEnable()){
            throw exception(APPLICATION_DISABLED);
        }

        SceneDO sceneDO = sceneMapper.selectByCode(scene);
        if (sceneDO == null) {
            throw exception(SCENE_NOT_EXISTS);
        }

        if(!sceneDO.getSceneEnable()){
            throw exception(SCENE_DISABLED);
        }

        if (category == CodeLogCategory.APPLY) {
            return sceneDO.getApplyAuthId();
        } else if (category == CodeLogCategory.GENERATE) {
            return sceneDO.getGenerateAuthId();
        } else if (category == CodeLogCategory.DECODE) {

            if(null!=applyCategory&&applyCategory.equals(ApplyCategory.STATIC)){
                //解码静态码不需要授权
                return "";
            }

            return sceneDO.getDecodeAuthId();
        } else {
            throw exception(CODE_LOG_CATEGORY_NOT_EXISTS);
        }
    }



    private SceneDO getSceneDO(Long sceneId) {
        SceneDO sceneDO;
        String sceneDOStr = redisService.getStr(String.format(SCENE_CACHE_KEY, sceneId));
        if (StringUtils.isBlank(sceneDOStr)){
            sceneDO = sceneMapper.selectById(sceneId);
            if(sceneDO == null){
                throw exception(SCENE_NOT_EXISTS);
            }
        }else {
            sceneDO = com.alibaba.fastjson2.JSONObject.parseObject(sceneDOStr,SceneDO.class);
        }
        return sceneDO;
    }


    private ChannelDO getChannelDO(String channelCode) {
        ChannelDO channelDO;
        String str = redisService.getStr(String.format(CHANNEL_CACHE_KEY, channelCode));
        if (StringUtils.isBlank(str)){
            channelDO = channelMapper.selectByCode(channelCode);
            if (channelDO == null) {
                throw exception(CHANNEL_NOT_EXISTS);
            }
            redisService.set(String.format(CHANNEL_CACHE_KEY, channelDO.getChannelCode()), JSON.toJSONString(channelDO));
        }else {
            channelDO = com.alibaba.fastjson2.JSONObject.parseObject(str,ChannelDO.class);
        }
        return channelDO;
    }

    private ApplicationDO getApplication(String applicationCode) {
        String str = redisService.getStr(String.format(APPLICATION_CACHE_KEY,applicationCode));
        ApplicationDO applicationDO;
        if (StringUtils.isBlank(str)){
            applicationDO = applicationMapper.selectByCode(applicationCode);
            if (applicationDO == null) {
                throw exception(APPLICATION_NOT_EXISTS);
            }
            redisService.set(String.format(APPLICATION_CACHE_KEY, applicationDO.getApplicationCode()),JSON.toJSONString(applicationDO));
        }else {
            applicationDO = com.alibaba.fastjson2.JSONObject.parseObject(str,ApplicationDO.class);
        }
        return applicationDO;
    }

}