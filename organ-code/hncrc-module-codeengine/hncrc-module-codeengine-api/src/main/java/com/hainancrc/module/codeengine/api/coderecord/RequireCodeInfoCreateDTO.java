package com.hainancrc.module.codeengine.api.coderecord;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hainancrc.module.codeengine.enums.ApplyCategory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@ApiModel("生成码 Request VO")
@Data
public class RequireCodeInfoCreateDTO {

    @ApiModelProperty(value = "码应用id",required = true)
    @NotNull(message = "码应用id不能为空")
    private String applicationCode;

    @ApiModelProperty(value = "场景sceneCode",required = true)
    @NotNull(message = "场景sceneCode不能为空")
    private String sceneCode;

    @ApiModelProperty(value = "主体id",required = true)
    private String entityId;

    @ApiModelProperty(value = "主体",required = true)
    private String entity;

    @ApiModelProperty(value = "渠道ids",required = true)
    @NotNull(message = "渠道ids不能为空")
    private String[] channelCodes;

    @ApiModelProperty(value="码有效期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expiredTime;

    @ApiModelProperty(value = "申码描述",required = true)
    private String applyDescription;

    @ApiModelProperty(value = "码类型",required = true)
    private ApplyCategory codeCategory;
}