package com.hainancrc.module.codeengine.controller.publicapi;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.hainancrc.module.codeengine.api.coderecord.RequireCodeInfoCreateDTO;
import com.hainancrc.module.codeengine.api.coderecord.vo.CodeRecordGeneratorCodeInfoRespVO;
import com.hainancrc.module.codeengine.api.coderecord.vo.CodeRecordGeneratorCodeRespVO;
import com.hainancrc.module.codeengine.enums.ApplyCategory;
import com.hainancrc.module.codeengine.service.coderecord.CodeRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

import java.util.List;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.codeengine.api.common.KeyNameVO;
import com.hainancrc.module.codeengine.api.publicapi.dto.ApplyCreateDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.ApplyStatusDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.ApplyWithAuthDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.DecodeCreateDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.DecodeStatusDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.DecodeWithAuthDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.GenerateCreateDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.GenerateStatusDTO;
import com.hainancrc.module.codeengine.api.publicapi.dto.GenerateWithAuthDTO;
import com.hainancrc.module.codeengine.api.publicapi.vo.ApplyCreateRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.ApplyStatusRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.DecodeAuthRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.DecodeCreateRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.DecodeStatusRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.GenerateAuthRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.GenerateCreateRespVO;
import com.hainancrc.module.codeengine.api.publicapi.vo.GenerateStatusRespVO;
import com.hainancrc.module.codeengine.enums.CodeLogCategory;
import com.hainancrc.module.codeengine.service.publicapi.PublicApiService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "对外API")
@RestController
@RequestMapping("/codeengine/publicapi")
@Validated
public class PublicApiController {

    @Resource
    private PublicApiService apiService;

    @Resource
    private CodeRecordService codeRecordService;

    @GetMapping("/sceneList")
    @ApiOperation("场景列表(Id = 场景Code ,Name = 场景名)")
    public CommonResult<List<KeyNameVO>> sceneList(@RequestParam("application")String application) {
        return success(apiService.sceneList(application));
    }

    @GetMapping("/sceneAuthId")
    @ApiOperation("检索场景对应动作ID。")
    public CommonResult<String> getSceneAuthId(
            @RequestParam("application") String application,
            @RequestParam("scene") String scene,
            @RequestParam("category") CodeLogCategory category,
            @RequestParam("applyCategory") ApplyCategory applyCategory) {
        return success(apiService.getSceneAuthId(application, scene, category,applyCategory));
    }

    @PostMapping("/apply")
    @ApiOperation("申码流程")
    public CommonResult<ApplyCreateRespVO> createApply(@Valid @RequestBody ApplyCreateDTO createDTO) {
        return success(apiService.createApply(createDTO));
    }

    @PostMapping("/applyWithAuth")
    @ApiOperation("申码授权复核流程")
    public CommonResult<ApplyStatusRespVO> applyWithAuth(@Valid @RequestBody ApplyWithAuthDTO authDTO) {
        return success(apiService.applyWithAuth(authDTO));
    }

    @PostMapping("/applyStatus")
    @ApiOperation("申码状态查询")
    public CommonResult<ApplyStatusRespVO> applyStatus(@Valid @RequestBody ApplyStatusDTO statusDTO) {
        return success(apiService.applyStatus(statusDTO));
    }

    @PostMapping("/generate")
    @ApiOperation("亮码流程")
    public CommonResult<GenerateCreateRespVO> createGenerate(@Valid @RequestBody GenerateCreateDTO createDTO) {
        return success(apiService.createGenerate(createDTO));
    }

    @PostMapping("/generateWithAuth")
    @ApiOperation("亮码授权复核流程")
    public CommonResult<GenerateAuthRespVO> generateWithAuth(@Valid @RequestBody GenerateWithAuthDTO authDTO) {
        return success(apiService.generateWithAuth(authDTO));
    }

    @PostMapping("/generateStatus")
    @ApiOperation("亮码状态查询")
    public CommonResult<GenerateStatusRespVO> generateStatus(@Valid @RequestBody GenerateStatusDTO statusDTO) {
        return success(apiService.generateStatus(statusDTO));
    }
    

    @PostMapping("/decode")
    @ApiOperation("解码流程")
    public CommonResult<DecodeCreateRespVO> createDecode(@Valid @RequestBody DecodeCreateDTO createDTO) {
        return success(apiService.createDecode(createDTO));
    }

    @PostMapping("/decodeWithAuth")
    @ApiOperation("解码授权复核流程")
    public CommonResult<DecodeAuthRespVO> decodeWithAuth(@Valid @RequestBody DecodeWithAuthDTO authDTO) {
        return success(apiService.decodeWithAuth(authDTO));
    }

    @PostMapping("/decodeStatus")
    @ApiOperation("解码状态查询")
    public CommonResult<DecodeStatusRespVO> decodeStatus(@Valid @RequestBody DecodeStatusDTO statusDTO) {
        return success(apiService.decodeStatus(statusDTO));
    }

    @PostMapping("/requireCreatingCodeInfo")
    @ApiOperation("生成静态码信息并返回")
    public CommonResult<CodeRecordGeneratorCodeInfoRespVO> requireCreatingCodeInfo(@Valid @RequestBody RequireCodeInfoCreateDTO createDTO) {
        return success(codeRecordService.requireCreatingCodeInfo(createDTO));
    }

    @GetMapping("/createCodeByCodeContent")
    @ApiOperation("获取静态码图片内容")
    public CommonResult<CodeRecordGeneratorCodeRespVO> createCodeByCodeContent(@RequestParam("codeContent") String codeContent,
                                                                               @RequestParam(value = "sceneCodeUrl",required = false) String sceneCodeUrl) {
        return success(codeRecordService.createCodeImage(codeContent,sceneCodeUrl));
    }
}
