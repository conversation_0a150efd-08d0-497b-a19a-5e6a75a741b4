package com.hainancrc.module.codeengine.mapper.applicationscenerel;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.module.codeengine.entity.ApplicationSceneRelDO;
import org.apache.ibatis.annotations.Mapper;
import com.hainancrc.module.codeengine.api.applicationscenerel.dto.*;

/**
 * 应用场景多对多 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ApplicationSceneRelMapper extends BaseMapperX<ApplicationSceneRelDO> {

    default PageResult<ApplicationSceneRelDO> selectPage(ApplicationSceneRelPageDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<ApplicationSceneRelDO>()
                .eqIfPresent(ApplicationSceneRelDO::getApplicationId, reqDTO.getApplicationId())
                .eqIfPresent(ApplicationSceneRelDO::getSceneId, reqDTO.getSceneId())
                .orderByDesc(ApplicationSceneRelDO::getId));
    }

    default List<ApplicationSceneRelDO> selectList(ApplicationSceneRelExportListDTO reqDTO) {
        return selectList(new LambdaQueryWrapperX<ApplicationSceneRelDO>()
                .eqIfPresent(ApplicationSceneRelDO::getApplicationId, reqDTO.getApplicationId())
                .eqIfPresent(ApplicationSceneRelDO::getSceneId, reqDTO.getSceneId())
                .orderByDesc(ApplicationSceneRelDO::getId));
    }

    default List<ApplicationSceneRelDO> selectByApplicationId(Long id){
        return selectList(new LambdaQueryWrapperX<ApplicationSceneRelDO>()
                .eqIfPresent(ApplicationSceneRelDO::getApplicationId, id)
                .orderByDesc(ApplicationSceneRelDO::getId));
    }

    default Integer deleteBySceneId(Long id){
        return delete(new LambdaQueryWrapperX<ApplicationSceneRelDO>()
                .eqIfPresent(ApplicationSceneRelDO::getSceneId, id));
    }

    default List<ApplicationSceneRelDO> selectBySceneId(Long id){
        return selectList(new LambdaQueryWrapperX<ApplicationSceneRelDO>()
                .eqIfPresent(ApplicationSceneRelDO::getSceneId, id)
                .orderByDesc(ApplicationSceneRelDO::getId));
    }

}
