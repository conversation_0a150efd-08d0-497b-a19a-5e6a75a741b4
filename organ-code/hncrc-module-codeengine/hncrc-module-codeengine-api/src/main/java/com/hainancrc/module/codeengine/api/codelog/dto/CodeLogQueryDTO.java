package com.hainancrc.module.codeengine.api.codelog.dto;

import com.hainancrc.module.codeengine.enums.CodeLogCategory;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/01/18 10:38
 **/
@Data
@ToString
public class CodeLogQueryDTO implements Serializable {

    private Long pageNum = 0L;

    private Long pageSize = 10L;

    private CodeLogCategory codeLogType;

    private String creditCode;

    private Date startTime;

    private Date endTime;

    private String channel;

}
