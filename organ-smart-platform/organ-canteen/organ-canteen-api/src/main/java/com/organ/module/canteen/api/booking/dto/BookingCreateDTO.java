package com.organ.module.canteen.api.booking.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("创建预约信息 DTO")
public class BookingCreateDTO {

    @ApiModelProperty(value = "食堂id")
    private Long canteenId;

    @ApiModelProperty(value = "预约详情列表")
    private List<BookingCreateDetailDTO> detailList;

}
