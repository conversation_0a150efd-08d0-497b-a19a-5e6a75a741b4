package com.organ.module.common.utils.time;

import lombok.Getter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

@Getter
public class TimeRange {

    private static final DateTimeFormatter FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final LocalDateTime start;
    private final LocalDateTime end;


    public TimeRange(LocalDateTime start, LocalDateTime end) {
        if (start == null) {
            throw new IllegalArgumentException("Start time must not be null");
        }
        if (end == null) {
            throw new IllegalArgumentException("End time must not be null");
        }
        if (start.isAfter(end)) {
            throw new IllegalArgumentException("Start time must not be after end time");
        }
        this.start = start;
        this.end = end;
    }


    /**
     * Checks if the given time is within this range (inclusive).
     */
    public boolean contains(LocalDateTime time) {
        return !time.isBefore(start) && !time.isAfter(end);
    }

    public boolean isBefore(LocalDateTime time) {
        return end.isBefore(time);
    }

    public boolean isAfter(LocalDateTime time) {
        return start.isAfter(time);
    }


    public String getStartStr() {
        return start.format(FORMATTER);
    }

    public String getEndStr() {
        return end.format(FORMATTER);
    }

    public static TimeRange of(LocalDateTime start, LocalDateTime end) {
        return new TimeRange(start, end);
    }

    public static TimeRange ofDay(LocalDate date) {
        LocalDateTime start = date.atStartOfDay();
        LocalDateTime end = date.atTime(23, 59, 59);
        return new TimeRange(start, end);
    }

    public static TimeRange ofMonth(LocalDate date) {
        YearMonth yearMonth = YearMonth.from(date);
        LocalDate start = yearMonth.atDay(1);
        LocalDate end = yearMonth.atEndOfMonth();
        return new TimeRange(start.atStartOfDay(), end.atTime(23, 59, 59));
    }

    public static TimeRange ofYear(int year) {
        LocalDate start = LocalDate.of(year, 1, 1);
        LocalDate end = LocalDate.of(year, 12, 31);
        return new TimeRange(start.atStartOfDay(), end.atTime(23, 59, 59));
    }

    // ----- toString / equals / hashCode -----
    @Override
    public String toString() {
        return "TimeRange[" + getStartStr() + " ~ " + getEndStr() + "]";
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TimeRange timeRange = (TimeRange) o;
        return start.equals(timeRange.start) && end.equals(timeRange.end);
    }

    @Override
    public int hashCode() {
        return Objects.hash(start, end);
    }
}
