package com.hainancrc.module.codeengine.api.applicationchannelrel.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import com.hainancrc.framework.common.pojo.PageParam;


@ApiModel("应用渠道多对多分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApplicationChannelRelPageDTO extends PageParam {


    @ApiModelProperty(value = "应用ID")
    private Integer applicationId;

    @ApiModelProperty(value = "渠道ID")
    private Integer channelId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
