package com.hainancrc.module.codeengine.api.publicapi.vo;

import com.hainancrc.module.codeengine.enums.CodeLogStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("ApplyStatusRespVO")
@Data
public class ApplyStatusRespVO {

    @ApiModelProperty(value = "申码状态(authRequired/fail/success)", required = true)
    private CodeLogStatus applyStatus;
    
}
