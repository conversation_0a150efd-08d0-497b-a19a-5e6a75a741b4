package com.organ.module.canteen.reconciliation.controller;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.reconciliation.dto.ReconciliationPageDTO;
import com.organ.module.canteen.api.reconciliation.vo.ReconciliationPageRespVO;
import com.organ.module.canteen.api.reconciliation.vo.ReconciliationStatVO;
import com.organ.module.canteen.reconciliation.service.ReconciliationService;
import com.organ.module.enums.ReconciliationTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "对账管理")
@RestController
@RequestMapping("/reconciliation")
public class ReconciliationController {

    @Resource
    private ReconciliationService reconciliationService;

    @GetMapping("/stat")
    @ApiOperation("统计信息")
    public CommonResult<ReconciliationStatVO> getStatisticsInfo(@RequestParam Long canteenId,
                                                                @RequestParam ReconciliationTypeEnum type) {
        return CommonResult.success(reconciliationService.getStatisticsInfo(canteenId, type));
    }

    @GetMapping("/page")
    @ApiOperation("分页查询")
    public CommonResult<PageResult<ReconciliationPageRespVO>> getReconciliationPage(ReconciliationPageDTO pageDTO) {
        return CommonResult.success(reconciliationService.getReconciliationPage(pageDTO));
    }
}
