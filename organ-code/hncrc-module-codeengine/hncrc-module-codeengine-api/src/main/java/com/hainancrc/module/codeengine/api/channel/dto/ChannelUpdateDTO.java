package com.hainancrc.module.codeengine.api.channel.dto;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("渠道更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChannelUpdateDTO extends ChannelBaseDTO {

    @ApiModelProperty(value = "渠道ID", required = true)
    @NotNull(message = "渠道ID不能为空")
    private Integer id;

}
