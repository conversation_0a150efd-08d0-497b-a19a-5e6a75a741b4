package com.hainancrc.module.codeengine.api.applicationscenerel.dto;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("应用场景多对多更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApplicationSceneRelUpdateDTO extends ApplicationSceneRelBaseDTO {

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    private Integer id;

}
