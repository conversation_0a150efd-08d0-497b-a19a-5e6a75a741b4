package com.organ.module.canteen.api.booking.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("创建预约信息详情 DTO")
public class BookingCreateDetailDTO {

    @ApiModelProperty(value = "预约用餐日期", example = "2025-01-01")
    private LocalDate reserveDate;

    @ApiModelProperty(value = "预约的餐次id列表")
    private List<Long> mealPeriodIdList;
}
