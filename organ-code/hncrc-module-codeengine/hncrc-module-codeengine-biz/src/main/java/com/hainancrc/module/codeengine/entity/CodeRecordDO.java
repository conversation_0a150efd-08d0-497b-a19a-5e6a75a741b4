package com.hainancrc.module.codeengine.entity;

import com.hainancrc.module.codeengine.enums.ApplyCategory;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 码记录 DO
 *
 * <AUTHOR>
 */
@TableName("codeengine_code_record")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CodeRecordDO extends BaseDO {

    /**
     * 二维码key
     */
    @TableId(type = IdType.INPUT)
    private String codeKey;

    /**
     * 二维码地址
     */
    private String codeUrl;

    /**
     * 申码分类(dynamic/static)
     */
    private ApplyCategory applyCategory;

    /**
     * 申码记录ID
     */
    private Long applyId;
    /**
     * 扫码次数
     */
    private Integer scanCount;
    /**
     * 是否过期
     */
    private Boolean codeRecordExpired;


}
