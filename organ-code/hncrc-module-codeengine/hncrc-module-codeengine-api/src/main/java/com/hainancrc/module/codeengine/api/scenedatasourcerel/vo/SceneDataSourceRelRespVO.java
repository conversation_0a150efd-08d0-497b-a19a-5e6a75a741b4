package com.hainancrc.module.codeengine.api.scenedatasourcerel.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import com.hainancrc.module.codeengine.api.scenedatasourcerel.dto.*;

@ApiModel("场景数据源 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SceneDataSourceRelRespVO extends SceneDataSourceRelBaseDTO {

    @ApiModelProperty(value = "主键ID", required = true)
    private Integer id;

    @ApiModelProperty(value = "创建时间", required = true)
    private Date createTime;

}
