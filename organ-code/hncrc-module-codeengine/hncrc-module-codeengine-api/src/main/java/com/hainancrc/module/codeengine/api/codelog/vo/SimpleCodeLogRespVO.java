package com.hainancrc.module.codeengine.api.codelog.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hainancrc.module.codeengine.api.codelog.dto.CodeLogBaseDTO;
import com.hainancrc.module.codeengine.enums.CodeLogStatus;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/01/18 11:06
 **/
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimpleCodeLogRespVO {

    /**
     * 动静态码
     */
    private Integer type;

    /**
     * 记录类型
     */
    private String useType;

    private String entity;

    private String entityName;

    private String orderId;

    private String scanEntity;

    /**
     * 扫码实体名
     */
    private String scanEntityName;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 场景名称
     */
    private String scene;

    /**
     * 场景code
     */
    private String sceneNo;

    /**
     * 状态
     */
    private CodeLogStatus state;

    /**
     * 有效期
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date validityTime;
    /**
     * 是否过期
     */
    private int expired;

    private String remark;
    @ApiModelProperty(value = "扫描记录主键，自增", required = true)
    private Long id;

    @ApiModelProperty(value = "创建时间", required = true)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date createTime;


    @ApiModelProperty(value = "码创建人")
    private String creator;

    @ApiModelProperty(value = "操作人名")
    private String createName;
}
