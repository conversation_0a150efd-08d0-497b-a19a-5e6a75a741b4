package com.organ.module.canteen.booking.convert;

import com.organ.module.canteen.api.booking.dto.BookingCreateDTO;
import com.organ.module.canteen.booking.entity.BookingDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface BookingConverter {

    BookingConverter INSTANCE = Mappers.getMapper(BookingConverter.class);

    BookingDO convert(BookingCreateDTO createDTO);

}
