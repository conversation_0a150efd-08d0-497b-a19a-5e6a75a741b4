#!/bin/bash
deployNetwork=$DEPLOY_NETWORK
imageFile=$1
k8sDeploy=$2
imageFileName=$3
imageTagName=$4
k8sDeployName=$5
FETCH_PATH=192.168.193.14:6443

initFetch() {
	case "$deployNetwork" in
	"TEST")
		FETCH_PATH=192.168.193.14:6443
		;;
	"PROD")
		FETCH_PATH=192.168.194.12:6553
		;;
	*)
		# FETCH_PATH=192.168.193.14:6443
		FETCH_PATH=192.168.193.14:6443
		;;
	esac
}
initFetch

echo "当前拟上传URL路径是: $FETCH_PATH"
echo "当前拟上传镜像文件路径是: $imageFile"
echo "当前拟部署的yaml文件路径是: $k8sDeploy"
echo "当前拟上传镜像文件名称是: $imageFileName"
echo "当前拟上传镜像名称是: $imageTagName"
echo "当前拟部署的yaml文件名称是: $k8sDeployName"

curl -X POST \
	-F \
	"imageFile=@$imageFile" \
	-F \
	"k8sDeploy=@$k8sDeploy" \
	-F \
	"imageFileName=$imageFileName" \
	-F \
	"imageTagName=$imageTagName" \
	-F \
	"k8sDeployName=$k8sDeployName" \
	http://$FETCH_PATH/deploy
