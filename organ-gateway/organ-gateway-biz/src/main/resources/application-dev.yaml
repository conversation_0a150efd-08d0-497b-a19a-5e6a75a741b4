server:
    port: 8999
--- ###
spring:
    # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
    redis:
        host: *************
        port: 6379
        database: 0 # 数据库索引

# Actuator 监控端点的配置项
management:
    endpoints:
        web:
            base-path: /actuator # Actuator 提供的 API 接口的根目录。默认为 /actuator
            exposure:
                include: '*' # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。

--- #################### 定时任务相关配置 ####################

# Spring Boot Admin 配置项
spring:
    boot:
        admin:
            # Spring Boot Admin Client 客户端的相关配置
            client:
                url: http://127.0.0.1:${server.port}/${spring.boot.admin.context-path} # 设置 Spring Boot Admin Server 地址
                instance:
                    prefer-ip: true # 注册实例时，优先使用 IP
            # Spring Boot Admin Server 服务端的相关配置
            context-path: /admin # 配置 Spring

# 日志文件配置
logging:
    file:
        name: ${user.home}/logs/${spring.application.name}.log # 日志文件名，全路径
    level:
        # 配置自己写的 MyBatis Mapper 打印日志
        com.hainancrc.module.systemgateway: debug

--- #########注册中心##########
eureka:
    client:
        service-url:
            defaultZone: http://127.0.0.1:8098/eureka
        registry-fetch-interval-seconds: 30
        register-with-eureka: true
    instance:
        prefer-ip-address: true
        lease-expiration-duration-in-seconds: 30
        lease-renewal-interval-in-seconds: 30
