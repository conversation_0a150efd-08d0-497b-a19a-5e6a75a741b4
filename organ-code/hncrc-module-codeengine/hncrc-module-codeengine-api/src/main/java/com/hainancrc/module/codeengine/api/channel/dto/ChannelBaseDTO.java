package com.hainancrc.module.codeengine.api.channel.dto;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 渠道
*/
@Data
public class ChannelBaseDTO {


    @ApiModelProperty(value = "渠道code", required = true)
    @Pattern(regexp = "^[a-zA-Z0-9]{1,5}$", message = "渠道code格式不正确")
    private String channelCode;

    @ApiModelProperty(value = "渠道名称", required = true)
    @NotEmpty(message = "渠道名称不能为空")
    @Pattern(regexp = "^[^\\s]*$", message = "渠道名称不能包含空格")
    private String channelName;

    @ApiModelProperty(value = "渠道描述")
    private String channelDescription;

}
