package com.hainancrc.module.codeengine.mapper.scenedatasourcerel;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.module.codeengine.entity.SceneDataSourceRelDO;
import org.apache.ibatis.annotations.Mapper;
import com.hainancrc.module.codeengine.api.scenedatasourcerel.dto.*;

/**
 * 场景数据源 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SceneDataSourceRelMapper extends BaseMapperX<SceneDataSourceRelDO> {

    default PageResult<SceneDataSourceRelDO> selectPage(SceneDataSourceRelPageDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<SceneDataSourceRelDO>()
                .eqIfPresent(SceneDataSourceRelDO::getSceneId, reqDTO.getSceneId())
                .eqIfPresent(SceneDataSourceRelDO::getDataSourceId, reqDTO.getDataSourceId())
                .orderByDesc(SceneDataSourceRelDO::getId));
    }

    default List<SceneDataSourceRelDO> selectList(SceneDataSourceRelExportListDTO reqDTO) {
        return selectList(new LambdaQueryWrapperX<SceneDataSourceRelDO>()
                .eqIfPresent(SceneDataSourceRelDO::getSceneId, reqDTO.getSceneId())
                .eqIfPresent(SceneDataSourceRelDO::getDataSourceId, reqDTO.getDataSourceId())
                .orderByDesc(SceneDataSourceRelDO::getId));
    }

    default List<SceneDataSourceRelDO> selectBySceneIds(ArrayList<Long> sceneIds){
        return selectList(new LambdaQueryWrapperX<SceneDataSourceRelDO>()
                .inIfPresent(SceneDataSourceRelDO::getSceneId, sceneIds)
                .orderByDesc(SceneDataSourceRelDO::getId));
    }

    default List<SceneDataSourceRelDO> selectBySceneId(Long sceneId){
        return selectList(new LambdaQueryWrapperX<SceneDataSourceRelDO>()
                .eqIfPresent(SceneDataSourceRelDO::getSceneId, sceneId)
                .orderByDesc(SceneDataSourceRelDO::getId));
    }

    default Integer deleteBySceneId(Long id){
        return delete(new LambdaQueryWrapperX<SceneDataSourceRelDO>()
                .eqIfPresent(SceneDataSourceRelDO::getSceneId, id));
    }

}
