package com.hainancrc.module.codeengine.mapper.channel;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.codeengine.entity.ChannelDO;
import com.hainancrc.module.codeengine.mapper.CodeBaseMapper;

import org.apache.ibatis.annotations.Mapper;
import com.hainancrc.module.codeengine.api.channel.dto.*;

/**
 * 渠道 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ChannelMapper extends CodeBaseMapper<ChannelDO> {

    default PageResult<ChannelDO> selectPage(ChannelPageDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<ChannelDO>()
                .betweenIfPresent(ChannelDO::getCreateTime, reqDTO.getCreateTimeStart(), reqDTO.getCreateTimeEnd())
                .orderByDesc(ChannelDO::getId));
    }

    default ChannelDO selectByCode(String code){
        return selectOne(new LambdaQueryWrapperX<ChannelDO>().eq(ChannelDO::getChannelCode, code));
    }

    default Boolean existsByName(String name){
        return exists(new LambdaQueryWrapperX<ChannelDO>().eq(ChannelDO::getChannelName, name));
    }

    default ChannelDO selectByName(String name){
        return selectOne(new LambdaQueryWrapperX<ChannelDO>().eq(ChannelDO::getChannelName, name));
    }

}
