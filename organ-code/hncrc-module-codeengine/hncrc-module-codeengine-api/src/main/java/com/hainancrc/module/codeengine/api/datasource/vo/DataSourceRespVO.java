package com.hainancrc.module.codeengine.api.datasource.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hainancrc.module.codeengine.api.datasource.dto.*;

@ApiModel("数据源 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DataSourceRespVO extends DataSourceBaseDTO {

    @ApiModelProperty(value = "数据源ID", required = true)
    private Integer id;

    @ApiModelProperty(value = "创建时间", required = true)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")

    private Date createTime;

    @ApiModelProperty(value = "修改时间", required = true)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date updateTime;

}
