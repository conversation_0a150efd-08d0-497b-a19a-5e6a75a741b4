package com.hainancrc.module.codeengine.convert.codelog;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.codeengine.api.codelog.vo.*;
import com.hainancrc.module.codeengine.entity.CodeLogDO;

/**
 * 生解码操作记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface CodeLogConvert {

    CodeLogConvert INSTANCE = Mappers.getMapper(CodeLogConvert.class);

    CodeLogRespVO convert(CodeLogDO bean);

    List<CodeLogRespVO> convertList(List<CodeLogDO> list);

    PageResult<CodeLogRespVO> convertPage(PageResult<CodeLogDO> page);


}
