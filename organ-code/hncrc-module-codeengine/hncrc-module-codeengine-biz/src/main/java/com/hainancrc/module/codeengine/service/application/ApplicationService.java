package com.hainancrc.module.codeengine.service.application;

import java.util.*;
import javax.validation.*;
import com.hainancrc.module.codeengine.api.application.dto.*;
import com.hainancrc.module.codeengine.api.application.vo.*;
import com.hainancrc.module.codeengine.api.common.IdNameVO;
import com.hainancrc.module.codeengine.entity.ApplicationDO;
import com.hainancrc.framework.common.pojo.PageResult;

/**
 * 应用 Service 接口
 *
 * <AUTHOR>
 */
public interface ApplicationService {

    /**
     * 以 key-value pair 方式获取码应用内容
     */
    List<IdNameVO> getKeyValue(Boolean applicationEnable);

    /**
     * 创建应用
     *
     * @param createDTO 创建信息
     * @return 编号
     */
    Long createApplication(@Valid ApplicationCreateDTO createDTO);

    /**
     * 更新应用
     *
     * @param updateDTO 更新信息
     */
    void updateApplication(@Valid ApplicationUpdateDTO updateDTO);

    /**
     * 获得应用
     *
     * @param id 编号
     * @return 应用
     */
    ApplicationRespVO getApplication(Long id);

    /**
     * 获得应用列表
     *
     * @param ids 编号
     * @return 应用列表
     */
    List<ApplicationDO> getApplicationList(Collection<Long> ids);

    /**
     * 获得应用分页
     *
     * @param pageDTO 分页查询
     * @return 应用分页
     */
    PageResult<ApplicationRespVO> getApplicationPage(ApplicationPageDTO pageDTO);

    /**
     * 启用应用
     */
    Long enableApplication(Long id);

    /**
     * 禁用应用
     */
    Long disableApplication(Long id);

    /**
     * 关联应用
     */
    void relateScene(Long id, String sceneIds);

    /**
     * 关联数据源
     */
    void relateChannel(Long id, String channelIds);

    /**
     * 以 key-value pair 方式获取应用相关场景内容
     */
    List<IdNameVO> getSceneKeyValue(Long id);

}
