<template>
  <div class="scan-result-page">
    <div class="header-wrapper">
      <MobileHeader title="扫码就餐" bgColor="transparent" />
      <div class="header-gap"></div>
    </div>

    <div class="canteen-content-pd page-content">
      <div class="scan-result" @click.stop>
        <!-- 扫码结果 -->
        <div v-if="result" class="result-content success">
          <!-- 顶部 渐变背景+图片 -->
          <div class="resule-header">
            <img
              v-if="!result.success"
              src="@/assets/images/canteen/failed.png"
              class="result-img"
            />
            <img
              v-else
              src="@/assets/images/canteen/success.png"
              class="result-img"
            />
          </div>

          <!-- 扫码结果文本 -->
          <div class="result-text">
            {{ result.success ? "核销成功" : "核销失败" }}
          </div>

          <div class="content-pd">
            <!-- 扫码信息卡片 -->
            <div
              :class="[
                'booking-info-card',
                !result.success ? 'failed' : 'success',
              ]"
            >
              <div v-if="!result.success" class="failed-card">
                <div>{{ result.errorMessage }}</div>
              </div>

              <div v-else class="success-card">
                <div class="info-item">
                  <span class="label">用户姓名</span>
                  <span class="value">{{ result.bookingInfo.username }}</span>
                </div>
                <div class="info-item">
                  <span class="label">餐次类型</span>
                  <span class="value">{{ result.bookingInfo.mealName }}</span>
                </div>
                <div class="info-item">
                  <span class="label">使用时间</span>
                  <span class="value">{{
                    result.bookingInfo.checkinTime
                      ? dayjs(result.bookingInfo.checkinTime).format(
                          "YYYY/MM/DD HH:mm:ss"
                        )
                      : "-"
                  }}</span>
                </div>
              </div>
            </div>

            <div class="card-footer">
              <!-- 操作按钮 -->
              <div class="result-actions">
                <button
                  v-if="!result.success"
                  class="canteen-btn btn-primary action-btn"
                  @click="handleRescanQRCode"
                >
                  重新扫码
                </button>
                <button
                  v-else
                  class="canteen-btn btn-primary action-btn"
                  @click="handleContinueScan"
                >
                  继续扫码
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import MobileHeader from "@/components/MobileHeader/index.vue";
import { IConsumeResult } from "@/api/canteen/operations/types/consume";
import { consumeDiningCode } from "@/api/canteen/operations/consume";
import { BIZ_MODULE_ROUTE_PREFIX } from "@/views/canteen/configs/constants";
import { useToast } from "@/components/Toast";

/** 路由处理 */
const router = useRouter();
const route = useRoute();

/** 路由参数 */
const routeParams = computed(() => route.query);

/** 核销结果 */
const result = ref<IConsumeResult>();

/** loading 状态提示 */
const toastHook = useToast();

/** 是否正在核销 */
const consuming = ref(false);

watch(
  () => consuming.value,
  (val) => {
    if (val) {
      toastHook.loading("核销中...");
    } else {
      toastHook.clear();
    }
  },
  { immediate: true }
);

/** 核销就餐码 */
const handleConsumeDiningCode = async (code: string) => {
  try {
    if (consuming.value) return;
    console.log("核销就餐码:", code);
    consuming.value = true;
    result.value = await consumeDiningCode(code);
  } catch (error) {
    console.error("核销就餐码失败:", error);
  } finally {
    consuming.value = false;
  }
};

/** 监听路由参数变化核销就餐码 */
watch(
  () => routeParams.value,
  (newVal) => {
    if (newVal) {
      const bookingCode = newVal.bookingCode as string;
      if (bookingCode) {
        handleConsumeDiningCode(bookingCode);
      }
    }
  },
  { immediate: true, deep: true }
);

/**
 * 处理继续扫码按钮点击事件
 */
const handleContinueScan = () => {
  router.replace({
    path: `${BIZ_MODULE_ROUTE_PREFIX}/operations/scanQrCode`,
  });
};

/**
 * 处理重新扫码按钮点击事件
 */
const handleRescanQRCode = () => {
  router.replace({
    path: `${BIZ_MODULE_ROUTE_PREFIX}/operations/scanQrCode`,
  });
};
</script>

<style scoped lang="scss">
.scan-result-page {
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;

  .header-wrapper {
    flex-shrink: 0;
    flex-grow: 0;
  }

  .page-content {
    padding-top: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(
      100vh - var(--header-height) - var(--tabbar-height) -
        env(safe-area-inset-top)
    );
    overflow: hidden;
  }

  .height-full {
    flex-grow: 1;
  }
  .scan-result {
    background: #ffffff;
    border-radius: 10px;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .content-pd {
      padding: 20px;
    }

    .result-content {
      padding: 0 0 24px;
      text-align: center;
      position: relative;
      border-radius: 12px;

      .resule-header {
        height: 100px;
        padding-top: 12px;
        background: linear-gradient(
          180deg,
          rgba(177, 204, 246, 1) 0%,
          rgba(255, 255, 255, 0) 100%
        );

        .result-img {
          width: auto;
          height: 100%;
          object-fit: cover;
        }
      }

      .result-text {
        color: #383838;
        font-size: 20px;
        font-weight: 500;
        margin-top: 6px;
      }

      // 预约信息卡片
      .booking-info-card {
        border-radius: 4px;
        padding: 20px;
        text-align: left;
        // backdrop-filter: blur(10px);

        &.success {
          background-color: #f0f8ff;
        }

        &.failed {
          background-color: #fff5f5;
          color: #ff4d4f;
        }

        .card-header {
          margin-bottom: 16px;

          .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #383838;
            margin: 0;
          }
        }

        .info-item {
          display: flex;
          align-items: center;
          padding: 6px 0;

          .label {
            flex-shrink: 0;
            width: 80px;
            font-size: 16px;
            color: #808080;
          }

          .value {
            flex-grow: 1;
            font-size: 16px;
            color: #1d1e20;
          }
        }

        .failed-card {
          text-align: center;
          padding: 50px 0;
        }
      }

      .card-footer {
        padding-top: 30px;
        width: 100%;
        margin-bottom: 50px;
      }

      // 操作按钮
      .result-actions {
        width: 100%;
        display: flex;
        justify-content: center;
        gap: 12px;

        .action-btn {
          flex: 1;
          max-width: 86%;
          padding: 16px;
          font-size: 18px;
        }
      }
    }
  }
}
</style>
