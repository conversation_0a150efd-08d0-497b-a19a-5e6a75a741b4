apiVersion: apps/v1
kind: Deployment
metadata:
  name: dyck-code #项目名称
  namespace: docker_namespace
spec:
  selector:
    matchLabels:
      app: dyck-code #项目名称
  template:
    metadata:
      labels:
        app: dyck-code #项目名称
    spec:
      imagePullSecrets:
      - name: ctyun-register
      containers:
      - name: dyck-code #项目名称
        image: image_build_name
        env:
        - name: CUR_ENV
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: profiles.active
        - name: EUREKA_DEFAULT_ZONE #映射到application中的值
          valueFrom:
            configMapKeyRef:
              name: credit-dyck-config #configMap配置
              key: eureka.defaultzone #配置中的key
        - name: DB_MYSQL_URL
          valueFrom:
            configMapKeyRef:
              name: credit-dyck-config
              key: db.code.mysql.url
        - name: DB_MYSQL_NAME
          valueFrom:
            configMapKeyRef:
              name: credit-dyck-config
              key: db.mysql.name
        - name: DB_MYSQL_PASSWORD
          valueFrom:
            configMapKeyRef:
              name: credit-dyck-config
              key: db.mysql.password
        - name: DB_KINGES_URL
          valueFrom:
            configMapKeyRef:
              name: credit-dyck-config
              key: db.kinges.url
        - name: DB_KINGES_NAME
          valueFrom:
            configMapKeyRef:
              name: credit-dyck-config
              key: db.kinges.name
        - name: DB_KINGES_PASSWORD
          valueFrom:
            configMapKeyRef:
              name: credit-dyck-config
              key: db.kinges.password
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: credit-dyck-config
              key: redis.host
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: credit-dyck-config
              key: redis.port
        - name: REDIS_PASSWORD
          valueFrom:
            configMapKeyRef:
              name: credit-dyck-config
              key: redis.password
        volumeMounts:
        - mountPath: /home/<USER>/dyck-code/logs/
          name: log
        ports:
        - containerPort: 80
      volumes:
      - name: log
        hostPath:
          path: /home/<USER>/dyck-code/logs/
          type: ''
