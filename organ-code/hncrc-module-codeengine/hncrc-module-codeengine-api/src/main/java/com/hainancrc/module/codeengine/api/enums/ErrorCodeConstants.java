package com.hainancrc.module.codeengine.api.enums;

import com.hainancrc.framework.common.exception.ErrorCode;

public interface ErrorCodeConstants {

    ErrorCode APPLICATION_NOT_EXISTS = new ErrorCode(100001, "应用不存在");
    ErrorCode APPLICATION_NAME_EXISTS = new ErrorCode(100002, "应用名已经存在");
    ErrorCode APPLICATION_SCENE_REL_EXISTS = new ErrorCode(101003, "应用场景关系已经存在");
    ErrorCode APPLICATION_DATA_SOURCE_REL_EXISTS = new ErrorCode(101004, "应用数据源关系已经存在");
    ErrorCode APPLICATION_CHANNEL_REL_EXISTS = new ErrorCode(101005, "应用渠道关系已经存在");
    ErrorCode APPLICATION_SCENE_REL_NOT_EXISTS = new ErrorCode(101006, "应用场景关系不存在");
    ErrorCode APPLICATION_CHANNEL_REL_NOT_EXISTS = new ErrorCode(101007, "应用渠道关系不存在");
    ErrorCode APPLICATION_NOT_AUTH = new ErrorCode(101008, "应用未授权");
    ErrorCode APPLICATION_DISABLED = new ErrorCode(101009, "应用已禁用");


    ErrorCode SCENE_NOT_EXISTS = new ErrorCode(101001, "场景不存在");
    ErrorCode SCENE_NAME_EXISTS = new ErrorCode(101002, "场景名已经存在");
    ErrorCode SCENE_STATICE_OR_DYNAMIC_RULE_IS_NULL = new ErrorCode(101003, "动态码 每扫几次失效 或 多少分钟后失效 必须有一个不为空");
    ErrorCode SCENE_EXPIRED_TIME_ERROR = new ErrorCode(101004, "所选的码有效期不能超过场景的设置的有效期");
    ErrorCode SCENE_DISABLED = new ErrorCode(101005, "场景已禁用");

    ErrorCode CHANNEL_NOT_EXISTS = new ErrorCode(102001, "渠道不存在");
    ErrorCode CHANNEL_NAME_EXISTS = new ErrorCode(102002, "渠道名已经存在");

    ErrorCode DATA_SOURCE_NOT_EXISTS = new ErrorCode(103001, "数据源不存在");    
    ErrorCode DATA_SOURCE_NAME_EXISTS = new ErrorCode(103001, "数据源名已经存在");
    ErrorCode DATA_SOURCE_NOT_SUPPORT = new ErrorCode(103002, "数据源不支持");
    ErrorCode DATA_SOURCE_HEADER_PARSE_ERROR = new ErrorCode(103003, "数据源头解析错误");
    ErrorCode DATA_SOURCE_REQUEST_ERROR = new ErrorCode(103004, "数据源请求错误");
    ErrorCode DATA_SOURCE_REQUEST_STATUS_ERROR = new ErrorCode(103005, "数据源请求状态错误");
    ErrorCode DATA_SOURCE_REQUEST_BODY_ERROR = new ErrorCode(103006, "数据源请求body错误");
    ErrorCode DATA_SOURCE_REQUEST_METHOD_ERROR = new ErrorCode(103007, "数据源请求method错误");
    ErrorCode DATA_SOURCE_CODE_EXISTS = new ErrorCode(103008, "数据源code已经存在");


    ErrorCode APPLY_CATEGORY_NOT_EXISTS = new ErrorCode(104001, "申码分类不合法");
    ErrorCode APPLY_NOT_EXISTS = new ErrorCode(104002, "申码不存在");
    ErrorCode APPLY_STATUS_NOT_AUTH_REQUIRED = new ErrorCode(104003, "申码状态不是待授权");
    ErrorCode APPLY_STATUS_NOT_SUCCESS = new ErrorCode(104004, "申码状态不是成功");
    ErrorCode APPLY_CHANNEL_REL_NOT_EXISTS = new ErrorCode(101005, "申码渠道关系不存在");

    ErrorCode CODE_LOG_NOT_EXISTS = new ErrorCode(106001, "订单不存在");
    ErrorCode CODE_LOG_STATUS_NOT_SUCCESS = new ErrorCode(104004, "申码状态不是成功");
    ErrorCode CODE_LOG_STATUS_NOT_AUTH_REQUIRED = new ErrorCode(106002, "订单状态不是待授权");

    ErrorCode CODE_NOT_EXISTS = new ErrorCode(107001, "码无法解析");

    ErrorCode IMPORT_EXCEL_ERROR = new ErrorCode(180001, "导入excel错误");
    ErrorCode CODE_BATCH_CREATE_ERROR = new ErrorCode(180002, "批量生码错误");
    ErrorCode CODE_RECORD_NOT_EXISTS = new ErrorCode(180003, "码记录不存在");
    ErrorCode CODE_READ_ERROR  = new ErrorCode(180004, "码无法解析");

    ErrorCode BATCH_GENERATE_NOT_EXISTS = new ErrorCode(190001, "批次不存在");


    
    ErrorCode CODE_LOG_CATEGORY_NOT_EXISTS = new ErrorCode(990002, "动作分类不存在");

}
