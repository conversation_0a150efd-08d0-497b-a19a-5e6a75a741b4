package com.hainancrc.module.codeengine.configuration;

import com.hainancrc.module.authorization.api.authorizationcenter.AuthorizationCenterApi;
import com.hainancrc.module.upload.api.upload.UploadFileApi;

import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

/**
 * EnableFeignClients 中引入UploadFileApi.class 和 PublicApi.class 允许远程调用
 */
@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = { UploadFileApi.class, AuthorizationCenterApi.class })
public class RpcConfig {

}
