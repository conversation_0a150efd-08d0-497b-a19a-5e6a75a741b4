package com.hainancrc.module.codeengine.service.codelog;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import com.hainancrc.module.codeengine.api.codelog.dto.CodeLogQueryDTO;
import com.hainancrc.module.codeengine.api.codelog.vo.SimpleCodeLogRespVO;
import com.hainancrc.module.codeengine.entity.ApplicationDO;
import com.hainancrc.module.codeengine.entity.CodeLogDO;
import com.hainancrc.module.codeengine.entity.SceneDO;
import com.hainancrc.module.codeengine.enums.ApplyCategory;
import com.hainancrc.module.codeengine.enums.CodeLogCategory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.hainancrc.module.codeengine.api.codelog.dto.CodeLogPageDTO;
import com.hainancrc.module.codeengine.api.codelog.vo.CodeLogRespVO;
import com.hainancrc.module.codeengine.entity.ChannelDO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.codeengine.mapper.application.ApplicationMapper;
import com.hainancrc.module.codeengine.mapper.channel.ChannelMapper;
import com.hainancrc.module.codeengine.mapper.codelog.CodeLogMapper;
import com.hainancrc.module.codeengine.mapper.scene.SceneMapper;

/**
 * Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CodeLogServiceImpl implements CodeLogService {

    @Resource
    private CodeLogMapper codeLogMapper;

    @Resource
    private ApplicationMapper applicationMapper;

    @Resource
    private ChannelMapper channelMapper;

    @Resource
    private SceneMapper sceneMapper;

    @Override
    public PageResult<CodeLogRespVO> getCodeLogPage(CodeLogPageDTO pageDTO) {

        Page<CodeLogRespVO> page = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        IPage<CodeLogRespVO> listOfvo = codeLogMapper.selectCodeLogPage(page, pageDTO);

        //将 listOfVO 中的 channelIdStr 转换为 Long[]
        listOfvo.getRecords().forEach(vo -> {
            String channelIdStr = vo.getChannelIdStr();
            if (channelIdStr != null) {
                String[] channelIdStrArr = channelIdStr.split(",");
                ArrayList<Long> channelIdArr = new ArrayList<>();
                for (int i = 0; i < channelIdStrArr.length; i++) {
                    if (channelIdStrArr[i].isEmpty()) {
                        continue;
                    }
                    channelIdArr.add(Long.parseLong(channelIdStrArr[i]));
                }
                vo.setChannelIds(channelIdArr.toArray(new Long[channelIdArr.size()]));
            }
        });

        //将所有 channel id 搜集一起
        ArrayList<Long> allChannelId = new ArrayList<>();
        listOfvo.getRecords().forEach(vo -> {
            Long[] channelIds = vo.getChannelIds();
            if (channelIds != null) {
                for (Long channelId : channelIds) {
                    allChannelId.add(channelId);
                }
            }
        });

        HashMap<Long, ChannelDO> hashChannelDO = new HashMap<>();
        if(allChannelId.size() > 0){
            List<ChannelDO> allChannelDOs = channelMapper.selectBatchIds(allChannelId);
            allChannelDOs.forEach(channelDO -> {
                hashChannelDO.put(channelDO.getId(), channelDO);
            });
        }

        //将 listOfvo 中对应的 channelName 填充
        listOfvo.getRecords().forEach(vo -> {
            Long[] channelIds = vo.getChannelIds();
            vo.setChannelName("");
            if(channelIds != null){
                for (Long channelId : channelIds) {
                    ChannelDO channelDO = hashChannelDO.get(channelId);
                    if (channelDO != null) {
                        vo.setChannelName(vo.getChannelName() + channelDO.getChannelName() + ",");
                    }
                }
                if (vo.getChannelName().endsWith(",")) {
                    vo.setChannelName(vo.getChannelName().substring(0, vo.getChannelName().length() - 1));
                }
            }
        });


        return new PageResult<>(listOfvo.getRecords(),  listOfvo.getTotal());
    }

    @Override
    public CodeLogRespVO getCodeLog(Long id) {
        CodeLogRespVO vo = codeLogMapper.selectCodeLogOne(id);
        String channelIdStr = vo.getChannelIdStr();
        if (channelIdStr != null) {
            String[] channelIdStrArr = channelIdStr.split(",");
            ArrayList<Long> channelIdArr = new ArrayList<>();
            for (int i = 0; i < channelIdStrArr.length; i++) {
                if (channelIdStrArr[i].isEmpty()) {
                    continue;
                }
                channelIdArr.add(Long.parseLong(channelIdStrArr[i]));
            }
            vo.setChannelIds(channelIdArr.toArray(new Long[channelIdArr.size()]));

            Long[] channelIds = vo.getChannelIds();
            vo.setChannelName("");
            for (Long channelId : channelIds) {
                ChannelDO channelDO = channelMapper.selectById(channelId);
                if (channelDO != null) {
                    vo.setChannelName(vo.getChannelName() + channelDO.getChannelName() + ",");
                }
            }
            if (vo.getChannelName().endsWith(",")) {
                vo.setChannelName(vo.getChannelName().substring(0, vo.getChannelName().length() - 1));
            }
        }
        return vo;
    }

    @Override
    public PageResult<SimpleCodeLogRespVO> querySimpleLogPage(CodeLogQueryDTO queryDTO) {
        log.info("查询码使用记录，{}",queryDTO);
        CodeLogCategory codeLogType = queryDTO.getCodeLogType();
        LambdaQueryWrapper<CodeLogDO> queryWrapper = new LambdaQueryWrapper<>();
        if (CodeLogCategory.DECODED.equals(codeLogType)){
            queryWrapper.eq(StringUtils.isNotBlank(queryDTO.getCreditCode()),CodeLogDO::getRequestEntityId, queryDTO.getCreditCode());
            queryWrapper.eq(CodeLogDO::getCodeLogCategory, CodeLogCategory.DECODE);
        }else {
            queryWrapper.eq(StringUtils.isNotBlank(queryDTO.getCreditCode()),CodeLogDO::getEntityId, queryDTO.getCreditCode());
            queryWrapper.eq(CodeLogDO::getCodeLogCategory, queryDTO.getCodeLogType());
        }
        queryWrapper.ge(Objects.nonNull(queryDTO.getStartTime()),BaseDO::getCreateTime,queryDTO.getStartTime())
                .le(Objects.nonNull(queryDTO.getEndTime()),BaseDO::getCreateTime,queryDTO.getEndTime())
                .like(StringUtils.isNotBlank(queryDTO.getChannel()),CodeLogDO::getChannelCode,queryDTO.getChannel())
                .orderByDesc(BaseDO::getCreateTime);
        Page<CodeLogDO> codeLogDOPage = codeLogMapper.selectPage(new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize()), queryWrapper);
        List<CodeLogDO> records = codeLogDOPage.getRecords();
        List<SimpleCodeLogRespVO> resp = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(records)){
            handleSimpleCodeLogDate(records, resp);
        }

        return new PageResult(resp,Long.valueOf(codeLogDOPage.getTotal()));
    }

    private void handleSimpleCodeLogDate(List<CodeLogDO> records, List<SimpleCodeLogRespVO> resp) {
        List<Long> applicationIds = new ArrayList<>();
        List<Long> sceneIds = new ArrayList<>();
        records.forEach(m -> {
            sceneIds.add(m.getSceneId());
            applicationIds.add(m.getApplicationId());
        });
        // 获取场景名称
        Map<Long, SceneDO> sceneNames = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sceneIds)){
            List<SceneDO> sceneDOS = sceneMapper.selectList(new LambdaQueryWrapper<SceneDO>().in(SceneDO::getId,sceneIds));
            sceneNames = sceneDOS.stream().collect(Collectors.toMap(SceneDO::getId,sceneDO -> sceneDO));
        }
        for (CodeLogDO record : records) {
            SimpleCodeLogRespVO simpleCodeLogRespVO = getSimpleCodelogRespVO(record, sceneNames);
            resp.add(simpleCodeLogRespVO);
        }
    }

    private SimpleCodeLogRespVO getSimpleCodelogRespVO(CodeLogDO record, Map<Long, SceneDO> sceneDOMap) {
        SimpleCodeLogRespVO simpleCodeLogRespVO = new SimpleCodeLogRespVO();
        simpleCodeLogRespVO.setId(record.getId());
        simpleCodeLogRespVO.setCreator(record.getCreator());
        simpleCodeLogRespVO.setCreateName(record.getCreateName());
        simpleCodeLogRespVO.setCreateTime(record.getCreateTime());
        simpleCodeLogRespVO.setEntity(record.getEntityId());
        simpleCodeLogRespVO.setEntityName(record.getEntity());
        if (Objects.nonNull(sceneDOMap.get(record.getSceneId()))){//场景
            simpleCodeLogRespVO.setSceneNo(sceneDOMap.get(record.getSceneId()).getSceneCode());
            simpleCodeLogRespVO.setScene(sceneDOMap.get(record.getSceneId()).getSceneName());
        }
        simpleCodeLogRespVO.setChannel(record.getChannelCode());
        simpleCodeLogRespVO.setScanEntityName(record.getRequestEntity());
        simpleCodeLogRespVO.setScanEntity(record.getRequestEntityId());
        Integer type = null;
        if (ApplyCategory.DYNAMIC.equals(record.getCodeCategory())){
            type = 2;
        }else if (ApplyCategory.STATIC.equals(record.getCodeCategory())){
            type = 1;
        }
        simpleCodeLogRespVO.setType(type);
        simpleCodeLogRespVO.setChannel(record.getChannelCode());
        simpleCodeLogRespVO.setOrderId(record.getCodeLogOrderId());
        simpleCodeLogRespVO.setState(record.getLogStatus());
        simpleCodeLogRespVO.setUseType(Objects.nonNull(record.getCodeLogCategory()) ? record.getCodeLogCategory().name() : null);
        simpleCodeLogRespVO.setValidityTime(record.getValidityTime());
        return simpleCodeLogRespVO;
    }


}
