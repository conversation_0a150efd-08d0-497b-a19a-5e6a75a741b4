:root {
  --color-primary: #409eff;
  --color-primary-plain-bg: #f0f8ff;

  --color-primary-light: #e1e8f0;
  --tabbar-height: 60px;
  --header-height: 48px;
}

/* 带导航栏的页面 */
.canteen-page-with-header {
  min-height: 100vh;
  /* 为头部留出空间 */
  padding-top: var(--header-height);
  overflow-x: hidden;
  overflow-y: auto;
  /* 安全区域适配 */
  padding-top: calc(var(--header-height) + env(safe-area-inset-top));
}

/* 带tabbar的页面 */
.canteen-page-with-tabbar {
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  /* 保持底部空间 */
  padding-bottom: var(--tabbar-height);
}

.canteen-page-nomral {
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  /* 安全区域适配 */
  padding-top: calc(var(--header-height) + env(safe-area-inset-top));
  /* 保持底部空间 */
  padding-bottom: var(--tabbar-height);
}

.canteen-page {
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  /* 安全区域适配 */
  padding-top: env(safe-area-inset-top);
}

/* 列表页面的容器(带头部和tabbar) */
.canteen-list-page {
  overflow-x: hidden;
  overflow-y: hidden;
  height: calc(
    100vh - var(--header-height) - var(--tabbar-height) -
      env(safe-area-inset-top)
  );
}

/* 头部留出空间 */
.header-gap {
  /* 安全区域适配 */
  height: calc(var(--header-height) + env(safe-area-inset-top));
}

/* 底部留出空间 */
.footer-gap {
  height: var(--tabbar-height);
}

.canteen-content-pd {
  padding: 20px 12px;
}

.canteen-content-px {
  padding: 0 16px;
}

.canteen-card {
  border-radius: 8px;
  padding: 20px;
  background: linear-gradient(180deg, #e6f3ff 0%, #ffffff 100%);
  border: 1px solid #ffffff;
}

.canteen-btn {
  padding: 12px 16px;
  border-radius: 8px;
  line-height: 1;
  cursor: pointer;
  border: 1px solid #e5e5e5;
  color: #808080;
  outline: none;
  transition: all 0.3s ease;
  text-align: center;
}
.canteen-btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.canteen-btn:hover:not(.disabled) {
  opacity: 0.8;
}
.canteen-btn.plain {
  color: var(--color-primary);
  background: var(--color-primary-plain-bg);
  border: 1px solid var(--color-primary);
}
.canteen-btn.rounded {
  border-radius: 9999px;
}
.canteen-btn.danger {
  color: #ffffff;
  background: #ff4d4f;
  border: 1px solid #ffc7c8;
}
.canteen-btn.small {
  padding: 10px 16px;
  font-size: 14px;
}

.canteen-link {
  color: var(--color-primary);
  text-decoration: none;
  cursor: pointer;
}
.canteen-link.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: #ffffff;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.btn-primary {
  background: var(--color-primary);
  color: #ffffff;
}

.btn-normal {
  padding: 16px;
  font-size: 18px;
}

.canteen-tag {
  color: #ffffff;
  background: var(--color-primary);
  padding: 6px 12px;
  border-radius: 9999px;
  font-size: 14px;
  line-height: 1;
}
.canteen-tag.plain {
  color: var(--color-primary);
  background: var(--color-primary-plain-bg);
}
