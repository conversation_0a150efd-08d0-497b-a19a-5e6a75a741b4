package com.hainancrc.module.codeengine.mapper.datasource;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.module.codeengine.entity.DataSourceDO;
import org.apache.ibatis.annotations.Mapper;
import com.hainancrc.module.codeengine.api.datasource.dto.*;

/**
 * 数据源 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DataSourceMapper extends BaseMapperX<DataSourceDO> {

    default PageResult<DataSourceDO> selectPage(DataSourcePageDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<DataSourceDO>()
                .likeIfPresent(DataSourceDO::getDataSourceName, reqDTO.getDataSourceName())
                .likeIfPresent(DataSourceDO::getDataSourceOwner, reqDTO.getDataSourceOwner())
                .eqIfPresent(DataSourceDO::getDataSourceStatus, reqDTO.getDataSourceStatus())
                .betweenIfPresent(DataSourceDO::getCreateTime, reqDTO.getCreateTimeStart(), reqDTO.getCreateTimeEnd())
                .orderByDesc(DataSourceDO::getId));
    }

    default Boolean existsByName(String name){
        return exists(new LambdaQueryWrapperX<DataSourceDO>().eq(DataSourceDO::getDataSourceName, name));
    }

}
