package com.hainancrc.module.codeengine.api.publicapi.vo;

import com.hainancrc.module.codeengine.enums.CodeLogStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("DecodeCreateRespVO")
@Data
public class DecodeCreateRespVO {

    @ApiModelProperty(value = "解码码订单ID", required = true)
    private String decodeOrderId;

    @ApiModelProperty(value = "解码状态(authRequired/fail/success)", required = true)
    private CodeLogStatus decodeStatus;

    @ApiModelProperty(value = "解码授权动作ID", required = false)
    private String authActionId;

}