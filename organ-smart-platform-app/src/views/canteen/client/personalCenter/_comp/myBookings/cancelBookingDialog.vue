<template>
  <div v-if="visible" class="user-info-overlay" @click="handleOverlayClick">
    <div class="user-info-dialog" @click.stop>
      <!-- 预约信息 -->
      <div class="info-content">
        <div class="header-section">
          <span class="info-title">确认取消</span>
        </div>

        <!-- 提示信息 -->
        <div class="tip-content">
          <i class="icon el-icon-warning"></i>
          <span class="tip-text">确认要取消这个预约吗？取消后将无法恢复。</span>
        </div>

        <!-- 关闭按钮 -->
        <div class="close-btn-wrapper">
          <div class="canteen-btn btn-secondary" @click="handleClose">
            我再想想
          </div>
          <div class="canteen-btn btn-primary" @click="handleConfirmCancel">
            确认取消
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IUserBooking } from "@/api/canteen/client/types/booking";

/**
 * 预约信息弹窗组件Props接口
 */
interface IProps {
  /** 是否显示弹窗 */
  value: boolean;
  /** 预约信息数据 */
  bookingInfo: IUserBooking;
}

/**
 * 组件事件接口
 */
interface IEmits {
  (e: "input", visible: boolean): void;
  /** 预约更新后事件（用于刷新父组件数据） */
  (e: "confirm", bookingInfo: IUserBooking): void;
}

const props = withDefaults(defineProps<IProps>(), {
  value: false,
});
const emit = defineEmits<IEmits>();

/** 是否显示弹窗 */
const visible = computed({
  get() {
    return props.value;
  },
  set(value) {
    emit("input", value);
  },
});

/**
 * 处理遮罩层点击事件
 */
const handleOverlayClick = () => {
  emit("input", false);
};

/** 关闭弹窗 */
const handleClose = () => {
  emit("input", false);
};

/** 请点击确认取消预约 */
const handleConfirmCancel = () => {
  emit("confirm", props.bookingInfo);
};
</script>

<style scoped lang="scss">
.user-info-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;

  .user-info-dialog {
    background: #ffffff;
    border-radius: 10px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;

    .info-content {
      overflow: hidden;
      padding: 0 20px;
      background: linear-gradient(180deg, #e6f3ff 0%, #ffffff 100%);

      .header-section {
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        overflow: hidden;

        .info-title {
          font-size: 20px;
          font-weight: 400;
          color: var(--color-primary);
          opacity: 1;
        }
      }

      .tip-content {
        display: flex;
        align-items: flex-start;
        justify-content: center;
        padding: 10px 0 20px;
        border-radius: 8px;

        .icon {
          font-size: 24px;
          color: var(--color-primary);
          margin-right: 8px;
          rotate: 180deg;
        }

        .tip-text {
          font-size: 18px;
          color: #383838;
        }
      }
    }

    .close-btn-wrapper {
      display: flex;
      justify-content: space-between;
      text-align: center;
      padding: 20px 0;
      gap: 20px;

      .canteen-btn {
        flex: 1;
      }
    }
  }
}
</style>
