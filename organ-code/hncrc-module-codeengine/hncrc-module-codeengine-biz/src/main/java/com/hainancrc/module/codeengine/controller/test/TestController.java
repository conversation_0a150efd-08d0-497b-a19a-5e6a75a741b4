package com.hainancrc.module.codeengine.controller.test;

import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageConfig;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.qrcode.QRCodeWriter;
import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.codeengine.api.common.KeyNameVO;

import cn.hutool.core.util.ZipUtil;

import static com.hainancrc.framework.common.pojo.CommonResult.success;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import javax.annotation.Resource;
import io.swagger.annotations.ApiOperation;
import lombok.var;
import springfox.documentation.annotations.ApiIgnore;

@ApiIgnore
@RestController
@RequestMapping("/codeengine/test")
@Validated
public class TestController {

    @Resource
    private ObjectMapper objectMapper;

    @GetMapping("/hello")
    @ApiOperation("hello_world")
    public CommonResult<String> helloWorld() throws JsonProcessingException {

        var vo = new KeyNameVO("1", "name");
        var json = objectMapper.writeValueAsString(vo);

        return success(json);

    }

    @PostMapping("/create_zip_file")
    @ApiOperation("create a zip file")
    public ResponseEntity<byte[]> createZipFile() {

        String text = "https://www.example.com";

        try {
            var qrCodeWriter = new QRCodeWriter();
            var bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, 300, 300);
            
            ByteArrayOutputStream pngOutputStream = new ByteArrayOutputStream();
            MatrixToImageConfig con = new MatrixToImageConfig( 0xFF000000 , 0xFFFFFFFF ) ;
            MatrixToImageWriter.writeToStream(bitMatrix, "PNG",pngOutputStream,con);

            var zipOutputStream = new ByteArrayOutputStream();
            var fileNames = new String[]{ "test.png"};
            var fileStreams = new ByteArrayInputStream[]{ new ByteArrayInputStream(pngOutputStream.toByteArray()) };
            ZipUtil.zip(zipOutputStream, fileNames, fileStreams);

            var buff = zipOutputStream.toByteArray();
            var headers = new HttpHeaders();
            headers.add("Content-Disposition", "attachment;filename=test.zip");
            headers.add("Content-Type", "application/octet-stream");

            return ResponseEntity.ok().headers(headers).body(buff);
            

        } catch ( WriterException | IOException e) {
            e.printStackTrace();
        }

        return null;
    }

}
