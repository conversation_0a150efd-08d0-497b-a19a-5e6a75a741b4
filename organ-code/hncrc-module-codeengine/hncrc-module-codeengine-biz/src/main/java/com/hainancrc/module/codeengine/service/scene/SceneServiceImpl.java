package com.hainancrc.module.codeengine.service.scene;

import com.alibaba.fastjson.JSON;
import com.hainancrc.framework.redis.service.RedisService;
import com.hainancrc.framework.security.SystemLoginUser;
import com.hainancrc.framework.security.core.util.TokenUtils;
import org.apache.poi.poifs.nio.DataSource;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.hainancrc.module.codeengine.api.common.IdNameVO;
import com.hainancrc.module.codeengine.api.scene.dto.*;
import com.hainancrc.module.codeengine.api.scene.vo.*;
import com.hainancrc.module.codeengine.entity.ApplicationDO;
import com.hainancrc.module.codeengine.entity.ApplicationSceneRelDO;
import com.hainancrc.module.codeengine.entity.DataSourceDO;
import com.hainancrc.module.codeengine.entity.SceneDO;
import com.hainancrc.module.codeengine.entity.SceneDataSourceRelDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hainancrc.framework.common.pojo.PageResult;

import com.hainancrc.module.codeengine.convert.scene.SceneConvert;
import com.hainancrc.module.codeengine.mapper.application.ApplicationMapper;
import com.hainancrc.module.codeengine.mapper.applicationscenerel.ApplicationSceneRelMapper;
import com.hainancrc.module.codeengine.mapper.datasource.DataSourceMapper;
import com.hainancrc.module.codeengine.mapper.scene.SceneMapper;
import com.hainancrc.module.codeengine.mapper.scenedatasourcerel.SceneDataSourceRelMapper;
import com.hainancrc.module.codeengine.utils.Helper;

import lombok.var;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.*;
/**
 * 场景 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SceneServiceImpl implements SceneService {

    @Resource
    private SceneMapper sceneMapper;

    @Resource
    private SceneDataSourceRelMapper sceneDataSourceRelMapper;

    @Resource
    private ApplicationSceneRelMapper applicationSceneRelMapper;

    @Resource
    private ApplicationMapper applicationMapper;

    @Resource
    private DataSourceMapper dataSourceMapper;

    @Resource
    private RedisService redisService;

    private static final String SCENE_CACHE_KEY = "code:engine:scene:%s";

    private void validateSceneNotExists(String name){
        if(sceneMapper.existsByName(name)){
            throw exception(SCENE_NAME_EXISTS);
        }
    }

    @Override
    public Long createScene(SceneCreateDTO createDTO) {

        this.validateSceneNotExists(createDTO.getSceneName());

        // 插入
        SceneDO scene = SceneConvert.INSTANCE.convert(createDTO);

        //dynamicExpiredTimes 或 dynamicExpiredMinute 必须有一个不为空
        if (scene.getDynamicExpiredTimes() == null && scene.getDynamicExpiredMinute() == null) {
            throw exception(SCENE_STATICE_OR_DYNAMIC_RULE_IS_NULL);
        }

        SystemLoginUser systemLoginUser= TokenUtils.getSysLoginUser(redisService);
        if(null!=systemLoginUser){
            scene.setPresenter(systemLoginUser.getUserAccount());
        }

        sceneMapper.insert(scene);

        updateSceneRelate(createDTO, scene, false);

        var code = "CJ" + String.format("%07d", scene.getId());
        scene.setSceneCode(code);

        sceneMapper.updateById(scene);


        redisService.set(String.format(SCENE_CACHE_KEY,scene.getId()), JSON.toJSONString(scene));

        // 返回
        return scene.getId();
    }

    @Override
    public void updateScene(SceneUpdateDTO updateDTO) {
        // 校验存在
        this.validateSceneExists(updateDTO.getId());
        // 更新
        SceneDO updateObj = SceneConvert.INSTANCE.convert(updateDTO);
        SystemLoginUser systemLoginUser= TokenUtils.getSysLoginUser(redisService);

        if(null!=systemLoginUser){
            updateObj.setPresenter(systemLoginUser.getUserAccount());
        }

        sceneMapper.updateById(updateObj);

        updateSceneRelate(updateDTO, updateObj, true);
        redisService.set(String.format(SCENE_CACHE_KEY,updateObj.getId()), JSON.toJSONString(updateObj));
    }

    private void updateSceneRelate(SceneCreateDTO createDTO, SceneDO scene, Boolean removeOldRelate) {

        if(removeOldRelate){
            sceneDataSourceRelMapper.deleteBySceneId(scene.getId());
            applicationSceneRelMapper.deleteBySceneId(scene.getId());
        }

        for(var dataSourceId : createDTO.getDataSourceId()){
            var rel = new SceneDataSourceRelDO();
            rel.setSceneId(scene.getId());
            rel.setDataSourceId(dataSourceId);
            sceneDataSourceRelMapper.insert(rel);
        }

        for(var applicationId : createDTO.getApplicationId()){
            var rel = new ApplicationSceneRelDO();
            rel.setSceneId(scene.getId());
            rel.setApplicationId(applicationId);
            applicationSceneRelMapper.insert(rel);
        }
    }

    private void validateSceneExists(Long id) {
        if (sceneMapper.selectById(id) == null) {
            throw exception(SCENE_NOT_EXISTS);
        }
    }

    @Override
    public SceneRespVO getScene(Long id) {

        var DO = sceneMapper.selectById(id);
        if(DO == null){
            throw exception(SCENE_NOT_EXISTS);
        }

        var VO = SceneConvert.INSTANCE.convert(DO);
        
        var dataSourceRelList = sceneDataSourceRelMapper.selectBySceneId(id);
        var dataSourceIdList = new ArrayList<Long>();
        for(var item : dataSourceRelList){
            dataSourceIdList.add(item.getDataSourceId());
        }

        var applicationRelList = applicationSceneRelMapper.selectBySceneId(id);
        var applicationIdList = new ArrayList<Long>();
        for(var item : applicationRelList){
            applicationIdList.add(item.getApplicationId());
        }

        VO.setDataSourceId(dataSourceIdList.toArray(new Long[dataSourceIdList.size()]));
        VO.setApplicationId(applicationIdList.toArray(new Long[applicationIdList.size()]));

        return VO;

    }

    @Override
    public List<SceneDO> getSceneList(Collection<Long> ids) {
        return sceneMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<SceneDO> getScenePage(ScenePageDTO pageDTO) {
        return sceneMapper.selectPage(pageDTO);
    }

    @Override
    public List<IdNameVO> getKeyValue(Integer status) {
        // 不传默认 启用 1
        Optional<Integer> optionalStatus = Optional.of(Optional.ofNullable(status).orElse(1));
        // 使用 stream 进行过滤，避免手动循环
        return sceneMapper.selectList(null).stream()
                // 根据 optionalStatus 的值，保留或跳过满足条件的元素
                .filter(item -> {
                            // 启用状态
                            if (optionalStatus.get().equals(1)) {
                                return item.getSceneEnable();
                            }
                            // 禁用状态
                            if (optionalStatus.get().equals(0)) {
                                return !item.getSceneEnable();
                            }
                            return true;
                        }
                )
                // 将符合条件的 item 转换为 IdNameVO，并收集到 List 中
                .map(item -> new IdNameVO(item.getId(), item.getSceneName()))
                .collect(Collectors.toList());
    }

    @Override
    public Long enableScene(Long id) {
        var DO = sceneMapper.selectById(id);
        if(DO == null){
            throw exception(SCENE_NOT_EXISTS);
        }
        DO.setSceneEnable(true);
        sceneMapper.updateById(DO);
        redisService.set(String.format(SCENE_CACHE_KEY,DO.getId()), JSON.toJSONString(DO));
        return DO.getId();
    }

    @Override
    public Long disableScene(Long id) {
        var DO = sceneMapper.selectById(id);
        if(DO == null){
            throw exception(SCENE_NOT_EXISTS);
        }
        DO.setSceneEnable(false);
        sceneMapper.updateById(DO);
        redisService.set(String.format(SCENE_CACHE_KEY,DO.getId()), JSON.toJSONString(DO));
        return DO.getId();
    }

    @Override
    public void relateApplication(Long id, String applicationIds) {
        var DO = sceneMapper.selectById(id);
        if(DO == null){
            throw exception(SCENE_NOT_EXISTS);
        }

        Long[] applicationIdArr = Helper.toLongArray(applicationIds, ",");

        List<ApplicationDO> applicationDOs = new ArrayList<>();

        if(applicationIdArr.length > 0){
            applicationDOs = applicationMapper.selectList(
                new LambdaQueryWrapper<ApplicationDO>()
                    .in(ApplicationDO::getId, (Object[]) applicationIdArr)
            );
        }

        if(applicationDOs.size() != applicationIdArr.length){
            throw exception(APPLICATION_NOT_EXISTS);
        }

        applicationSceneRelMapper.delete(
            new LambdaQueryWrapper<ApplicationSceneRelDO>()
                .eq(ApplicationSceneRelDO::getSceneId, id));

        for (var application : applicationDOs) {
            var rel = new ApplicationSceneRelDO();
            rel.setApplicationId(application.getId());
            rel.setSceneId(id);
            applicationSceneRelMapper.insert(rel);
        }
    }

    @Override
    public void relateDataSource(Long id, String dataSourceIds) {
        var DO = sceneMapper.selectById(id);
        if(DO == null){
            throw exception(SCENE_NOT_EXISTS);
        }

        Long[] dataSourceIdArr = Helper.toLongArray(dataSourceIds, ",");

        List<DataSourceDO> dataSourceDOs = new ArrayList<>();

        if(dataSourceIdArr.length > 0){
            dataSourceDOs = dataSourceMapper.selectList(
                new LambdaQueryWrapper<DataSourceDO>()
                    .in(DataSourceDO::getId, (Object[]) dataSourceIdArr)
            );
        }

        if(dataSourceDOs.size() != dataSourceIdArr.length){
            throw exception(DATA_SOURCE_NOT_EXISTS);
        }

        sceneDataSourceRelMapper.delete(
            new LambdaQueryWrapper<SceneDataSourceRelDO>()
                .eq(SceneDataSourceRelDO::getSceneId, id));

        for (var dataSource : dataSourceDOs) {
            var rel = new SceneDataSourceRelDO();
            rel.setSceneId(id);
            rel.setDataSourceId(dataSource.getId());
            sceneDataSourceRelMapper.insert(rel);
        }
    }

    


}
