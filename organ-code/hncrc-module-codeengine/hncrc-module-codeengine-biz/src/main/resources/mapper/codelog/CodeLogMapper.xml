<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hainancrc.module.codeengine.mapper.codelog.CodeLogMapper">

    <select id="selectCodeLogPage" resultType="com.hainancrc.module.codeengine.api.codelog.vo.CodeLogRespVO" parameterType="com.hainancrc.module.codeengine.api.codelog.dto.CodeLogPageDTO">

        SELECT 
            codeengine_code_log.* ,
            codeengine_application.application_name,
            codeengine_scene.scene_name
        FROM codeengine_code_log
        INNER JOIN codeengine_application ON codeengine_application.id = codeengine_code_log.application_id
        INNER JOIN codeengine_scene ON codeengine_scene.id = codeengine_code_log.scene_id
        WHERE 1=1

        <if test="dto.createTimeEnd != null and dto.createTimeStart != null">
            AND codeengine_code_log.create_time BETWEEN #{dto.createTimeStart} AND #{dto.createTimeEnd}
        </if>

        <if test="dto.codeLogCategorys != null and dto.codeLogCategorys.length != 0">
            AND code_log_category IN <foreach collection="dto.codeLogCategorys" item="item" open="(" separator="," close=")">#{item}</foreach>
        </if>

        <if test="dto.channelIds != null and dto.channelIds.length != 0">
            AND <foreach collection="dto.channelIds" item="item" open="(" separator=" OR " close=")">channel_id_str LIKE CONCAT('%,', #{item}, ',%')</foreach>
        </if>

        <if test="dto.applicationIds != null and dto.applicationIds.length != 0">
            AND application_id IN <foreach collection="dto.applicationIds" item="item" open="(" separator="," close=")">#{item}</foreach>
        </if>

        <if test="dto.sceneIds != null and dto.sceneIds.length != 0">
            AND scene_id IN <foreach collection="dto.sceneIds" item="item" open="(" separator="," close=")">#{item}</foreach>
        </if>

        <if test="dto.entityId != null">
            AND entity_id = #{dto.entityId}
        </if>

        <if test="dto.entity != null">
            AND entity = #{dto.entity}
        </if>

        <if test="dto.logStatus != null">
            AND log_status = #{dto.logStatus}
        </if>

        ORDER BY Id DESC
    
    </select>
    <select id="selectCodeLogOne" resultType="com.hainancrc.module.codeengine.api.codelog.vo.CodeLogRespVO" parameterType="Long">

        SELECT 
            codeengine_code_log.* ,
            codeengine_application.application_name,
            codeengine_scene.scene_name
        FROM codeengine_code_log
        INNER JOIN codeengine_application ON codeengine_application.id = codeengine_code_log.application_id
        INNER JOIN codeengine_scene ON codeengine_scene.id = codeengine_code_log.scene_id
        WHERE codeengine_code_log.id = #{id}
    
    </select>

</mapper>
