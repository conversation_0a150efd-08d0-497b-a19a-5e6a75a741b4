package com.organ.module.canteen.api.booking.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@ApiModel("预约失败 RespVO")
@NoArgsConstructor
@AllArgsConstructor
public class ReserveFailedRespVO {

    @ApiModelProperty(value = "预约日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate mealDate;

    @ApiModelProperty(value = "预约餐次")
    private Long mealPeriodId;

}
