package  com.hainancrc.module.codeengine.api.scene.dto;

import lombok.*;
import io.swagger.annotations.*;

@ApiModel("场景创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SceneCreateDTO extends SceneBaseDTO {

    @ApiModelProperty(value = "数据源Id")
    private Long[] dataSourceId;

    @ApiModelProperty(value = "应用Id")
    private Long[] applicationId;

}
