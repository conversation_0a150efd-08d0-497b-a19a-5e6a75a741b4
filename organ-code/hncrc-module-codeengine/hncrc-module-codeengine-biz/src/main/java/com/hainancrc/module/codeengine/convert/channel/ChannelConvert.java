package com.hainancrc.module.codeengine.convert.channel;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.codeengine.api.channel.dto.*;
import com.hainancrc.module.codeengine.api.channel.vo.*;
import com.hainancrc.module.codeengine.entity.ChannelDO;

/**
 * 渠道 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ChannelConvert {

    ChannelConvert INSTANCE = Mappers.getMapper(ChannelConvert.class);

    ChannelDO convert(ChannelCreateDTO bean);

    ChannelDO convert(ChannelUpdateDTO bean);

    ChannelRespVO convert(ChannelDO bean);

    List<ChannelRespVO> convertList(List<ChannelDO> list);

    PageResult<ChannelRespVO> convertPage(PageResult<ChannelDO> page);


}
