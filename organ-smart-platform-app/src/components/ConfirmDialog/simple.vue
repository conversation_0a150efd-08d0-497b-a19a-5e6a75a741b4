<template>
  <div
    v-if="visible"
    class="confirm-dialog-overlay"
    @click="handleOverlayClick"
  >
    <div class="confirm-dialog-container" :style="{ width: width }" @click.stop>
      <!-- 弹框头部 -->
      <div class="confirm-dialog-header">
        <slot name="title">
          <div class="dialog-title">{{ title }}</div>
        </slot>

        <button v-if="showClose" class="close-btn" @click="handleClose">
          ×
        </button>
      </div>

      <!-- 弹框内容 -->
      <div class="confirm-dialog-body">
        <slot>
          <div class="dialog-content">{{ content }}</div>
        </slot>
      </div>

      <!-- 弹框底部 -->
      <div class="confirm-dialog-footer">
        <slot name="footer">
          <div class="dialog-actions">
            <button
              v-if="showCancel"
              class="btn btn-cancel"
              @click="handleCancel"
            >
              {{ cancelText }}
            </button>
            <button
              v-if="showConfirm"
              class="btn btn-confirm"
              :class="`btn-${confirmType}`"
              @click="handleConfirm"
            >
              {{ confirmText }}
            </button>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";

/**
 * 简化版确认弹框组件
 * 使用Vue 2.7兼容的写法
 */
export default defineComponent({
  name: "SimpleConfirmDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "确认操作",
    },
    content: {
      type: String,
      default: "确定要执行此操作吗？",
    },
    confirmText: {
      type: String,
      default: "确定",
    },
    cancelText: {
      type: String,
      default: "取消",
    },
    confirmType: {
      type: String,
      default: "primary",
      validator: (value: string) =>
        ["primary", "success", "warning", "danger", "info"].includes(value),
    },
    showCancel: {
      type: Boolean,
      default: true,
    },
    showConfirm: {
      type: Boolean,
      default: true,
    },
    width: {
      type: String,
      default: "400px",
    },
    closeOnClickModal: {
      type: Boolean,
      default: true,
    },
    closeOnPressEscape: {
      type: Boolean,
      default: true,
    },
    showClose: {
      type: Boolean,
      default: true,
    },
  },
  emits: ["update:visible", "confirm", "cancel", "close"],
  setup(props, { emit }) {
    /**
     * 处理遮罩层点击事件
     */
    const handleOverlayClick = () => {
      if (props.closeOnClickModal) {
        handleClose();
      }
    };

    /**
     * 处理关闭事件
     */
    const handleClose = () => {
      emit("update:visible", false);
      emit("close");
    };

    /**
     * 处理确认按钮点击事件
     */
    const handleConfirm = () => {
      emit("confirm");
    };

    /**
     * 处理取消按钮点击事件
     */
    const handleCancel = () => {
      emit("cancel");
      handleClose();
    };

    /**
     * 监听ESC键盘事件
     */
    const handleKeydown = (event: KeyboardEvent) => {
      if (event.key === "Escape" && props.closeOnPressEscape && props.visible) {
        handleClose();
      }
    };

    // 组件挂载时添加键盘事件监听
    onMounted(() => {
      document.addEventListener("keydown", handleKeydown);
    });

    // 组件卸载时移除键盘事件监听
    onUnmounted(() => {
      document.removeEventListener("keydown", handleKeydown);
    });

    return {
      handleOverlayClick,
      handleClose,
      handleConfirm,
      handleCancel,
    };
  },
});
</script>

<style scoped lang="scss">
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;

  .confirm-dialog-container {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    max-width: 90vw;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    animation: dialogFadeIn 0.3s ease-out;

    // 弹框头部
    .confirm-dialog-header {
      position: relative;
      padding: 20px 24px 16px;
      border-bottom: 1px solid #f0f0f0;

      .dialog-title {
        font-size: 18px;
        font-weight: 600;
        color: #1d1e20;
        margin: 0;
        line-height: 1.4;
      }

      .close-btn {
        position: absolute;
        top: 16px;
        right: 16px;
        width: 32px;
        height: 32px;
        border: none;
        background: transparent;
        color: #999;
        cursor: pointer;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        font-size: 20px;

        &:hover {
          background: #f5f5f5;
          color: #666;
        }
      }
    }

    // 弹框内容
    .confirm-dialog-body {
      flex: 1;
      padding: 20px 24px;
      overflow-y: auto;

      .dialog-content {
        font-size: 16px;
        color: #666;
        line-height: 1.6;
        margin: 0;
        white-space: pre-line;
      }
    }

    // 弹框底部
    .confirm-dialog-footer {
      padding: 16px 24px 24px;
      border-top: 1px solid #f0f0f0;

      .dialog-actions {
        display: flex;
        gap: 12px;
        justify-content: flex-end;

        .btn {
          padding: 10px 20px;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          border: none;
          cursor: pointer;
          transition: all 0.2s ease;
          min-width: 80px;

          &.btn-cancel {
            background: #f5f5f5;
            color: #666;

            &:hover {
              background: #e8e8e8;
            }
          }

          &.btn-confirm {
            color: #ffffff;

            &.btn-primary {
              background: var(--color-primary, #409eff);

              &:hover {
                opacity: 0.8;
              }
            }

            &.btn-success {
              background: #67c23a;

              &:hover {
                opacity: 0.8;
              }
            }

            &.btn-warning {
              background: #e6a23c;

              &:hover {
                opacity: 0.8;
              }
            }

            &.btn-danger {
              background: #f56c6c;

              &:hover {
                opacity: 0.8;
              }
            }

            &.btn-info {
              background: #909399;

              &:hover {
                opacity: 0.8;
              }
            }
          }
        }
      }
    }
  }
}

// 弹框动画
@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// 移动端适配
@media (max-width: 768px) {
  .confirm-dialog-overlay {
    padding: 10px;

    .confirm-dialog-container {
      width: 100% !important;
      max-width: none;
      margin: 0;

      .confirm-dialog-header {
        padding: 16px 20px 12px;

        .dialog-title {
          font-size: 16px;
          padding-right: 40px;
        }

        .close-btn {
          top: 12px;
          right: 12px;
          width: 28px;
          height: 28px;
        }
      }

      .confirm-dialog-body {
        padding: 16px 20px;

        .dialog-content {
          font-size: 14px;
        }
      }

      .confirm-dialog-footer {
        padding: 12px 20px 20px;

        .dialog-actions {
          flex-direction: column-reverse;

          .btn {
            width: 100%;
            padding: 12px 20px;
            font-size: 16px;
          }
        }
      }
    }
  }
}
</style>
