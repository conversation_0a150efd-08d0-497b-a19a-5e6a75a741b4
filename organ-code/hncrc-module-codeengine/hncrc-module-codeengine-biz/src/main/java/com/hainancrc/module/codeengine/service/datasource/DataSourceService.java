package com.hainancrc.module.codeengine.service.datasource;

import java.util.*;
import javax.validation.*;

import com.hainancrc.module.codeengine.api.common.IdNameVO;
import com.hainancrc.module.codeengine.api.datasource.dto.*;
import com.hainancrc.module.codeengine.entity.DataSourceDO;
import com.hainancrc.framework.common.pojo.PageResult;

/**
 * 数据源 Service 接口
 *
 * <AUTHOR>
 */
public interface DataSourceService {
    
    /**
     * 以 key-value pair 方式获取数据源内容
     */
    List<IdNameVO> getKeyValue();

    /**
     * 创建数据源
     *
     * @param createDTO 创建信息
     * @return 编号
     */
    Long createDataSource(@Valid DataSourceCreateDTO createDTO);

    /**
     * 更新数据源
     *
     * @param updateDTO 更新信息
     */
    void updateDataSource(@Valid DataSourceUpdateDTO updateDTO);

    /**
     * 删除数据源
     *
     * @param id 编号
     */
    void deleteDataSource(Long id);

    /**
     * 获得数据源
     *
     * @param id 编号
     * @return 数据源
     */
    DataSourceDO getDataSource(Long id);

    /**
     * 数据源链接测试
     * @param ids
     * @return
     */
    String testDataSource(Long id);

    /**
     * 获得数据源列表
     *
     * @param ids 编号
     * @return 数据源列表
     */
    List<DataSourceDO> getDataSourceList(Collection<Long> ids);

    /**
     * 获得数据源分页
     *
     * @param pageDTO 分页查询
     * @return 数据源分页
     */
    PageResult<DataSourceDO> getDataSourcePage(DataSourcePageDTO pageDTO);

    /**
     * 获得数据源内容
     * @param id
     * @return
     */
    String requestDataSource(Long id);

}
