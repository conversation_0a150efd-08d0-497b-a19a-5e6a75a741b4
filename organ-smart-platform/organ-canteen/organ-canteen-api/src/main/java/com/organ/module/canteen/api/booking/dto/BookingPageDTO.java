package com.organ.module.canteen.api.booking.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hainancrc.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class BookingPageDTO extends PageParam {

    @ApiModelProperty(value = "用户姓名")
    private String username;

    @ApiModelProperty(value = "所属部门")
    private String belongUnit;

    @ApiModelProperty(value = "催发食堂")
    private String requestCanteen;

    @ApiModelProperty(value = "餐次")
    private String meal;

    @ApiModelProperty(value = "开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty(value = "状态")
    private String status;
}
