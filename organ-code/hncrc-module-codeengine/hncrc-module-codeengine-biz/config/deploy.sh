buildTime=$(date "+%Y%m%d%H%M%S")
tempFileName=$(echo "$imageName" | sed 's#.*/##')
tempFileName=$tempFileName-$buildTime
imageName="${imageName}:${buildTime}"
dockerNamespace=${namespace}
k8sConfig="prod"
kubeConfigPath="/opt/dyck/.kube/config"

# 引用Jenkins凭证中的用户名和密码
DOCKER_LOGIN_NAME=${DOCKER_LOGIN_NAME}
DOCKER_LOGIN_PASSWORD=${DOCKER_LOGIN_PASSWORD}
delimiter="-"

set -e

buildImage() {
	docker build -t $imageName .
	sed -i "s!image_build_name!$imageName!g; s!docker_namespace!$dockerNamespace!g" config/deploy$delimiter$k8sConfig.yaml

	docker save -o $WORKSPACE/$tempFileName.tar $imageName
	# echo $DOCKER_LOGIN_PASSWORD | docker login -u $DOCKER_LOGIN_NAME --password-stdin harbor.ffcs.cn
	# echo $DOCKER_LOGIN_PASSWORD | docker login -u $DOCKER_LOGIN_NAME --password-stdin 10.111.240.24
	# docker push $imageName
	# echo "当前拟上传镜像文件路径是: $imageFile"
	# echo "当前拟部署的yaml文件路径是: $k8sDeploy"
	# echo "当前拟上传镜像文件名称是: $imageFileName"
	# echo "当前拟上传镜像名称是: $imageTagName"
	# echo "当前拟部署的yaml文件名称是: $k8sDeployName"
	sh config/fetch.sh "${WORKSPACE}/$tempFileName.tar" "config/deploy$delimiter$k8sConfig.yaml" "$tempFileName.tar" "$imageName" "deploy$delimiter$k8sConfig.yaml"
	rm -rf "${WORKSPACE}/$tempFileName.tar"
}

# doK8sDeploy() {
# 	# sed -i "s!image_build_name!$imageName!g; s!docker_namespace!$dockerNamespace!g" config/deploy$delimiter$k8sConfig.yaml
# 	# kubectl --kubeconfig $kubeConfigPath apply -f config/deploy$delimiter$k8sConfig.yaml -n $dockerNamespace
# }

buildImage
# doK8sDeploy
