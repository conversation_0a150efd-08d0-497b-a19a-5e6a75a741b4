package com.hainancrc.module.codeengine.api.publicapi.vo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hainancrc.module.codeengine.enums.CodeLogStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("GenerateStatusRespVO")
@Data
public class GenerateStatusRespVO {

    @ApiModelProperty(value = "亮码状态(authRequired/fail/success)", required = true)
    private CodeLogStatus generateStatus;

    @ApiModelProperty(value = "亮码内容", required = false)
    private String codeUrl;
    
    @ApiModelProperty(value = "过期时间", required = false)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date expiredTime;

    @ApiModelProperty(value = "过期时间戳", required = false)
    private Date expiredTimeStamp;  //实际输出的时候会转换成时间戳
}