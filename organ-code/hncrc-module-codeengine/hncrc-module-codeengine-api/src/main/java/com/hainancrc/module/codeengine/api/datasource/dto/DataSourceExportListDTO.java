package  com.hainancrc.module.codeengine.api.datasource.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;


@ApiModel(value = "数据源 Excel 导出 Request VO", description = "参数和 DataSourcePageReqVO 是一致的")
@Data
public class DataSourceExportListDTO {

    @ApiModelProperty(value = "数据源名称")
    private String dataSourceName;
    @ApiModelProperty(value = "数据源描述")
    private String dataSourceDescription;
    @ApiModelProperty(value = "数据源分类(API/OTHER)")
    private String dataSourceCategory;
    @ApiModelProperty(value = "数据源API地址")
    private String dataSourceUrl;
    @ApiModelProperty(value = "数据源API请求方式(GET/POST/PUT/DELETE)")
    private String dataSourceMethod;
    @ApiModelProperty(value = "数据源API请求参数")
    private String queryParameters;
    @ApiModelProperty(value = "数据源API请求头")
    private String headerParameters;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
