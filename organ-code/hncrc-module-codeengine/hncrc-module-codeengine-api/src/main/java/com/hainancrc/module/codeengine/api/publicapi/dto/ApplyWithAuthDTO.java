package  com.hainancrc.module.codeengine.api.publicapi.dto;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("申码授权验证 Request VO")
@Data
public class ApplyWithAuthDTO {

    @ApiModelProperty(value = "应用Code", required = true)
    @NotNull(message = "应用Code不能为空")
    private String application;
    
    @ApiModelProperty(value = "申码订单号")
    @NotEmpty(message = "申码订单号不能为空")
    private String applyOrderId;

    @ApiModelProperty(value = "授权订单号", required = false)
    private String authOrderId;

}
