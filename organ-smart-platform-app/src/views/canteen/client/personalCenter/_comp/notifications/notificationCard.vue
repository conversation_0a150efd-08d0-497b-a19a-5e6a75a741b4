<template>
  <div class="notification-card" @click.stop="handleViewDetail">
    <i class="el-icon-info" style="color: var(--color-primary)"></i>
    <div class="content-wrapper">
      <div class="card-header">
        <div class="title-wrapper">
          <div class="title-text">
            {{ notificationInfo.title }}
          </div>
        </div>
        <!-- 发布日期 -->
        <div v-if="notificationInfo.publishTime" class="publish-time">
          {{ dayjs(notificationInfo.publishTime).format("YYYY年M月D日") }}
        </div>
      </div>
      <div
        ref="contentRef"
        class="notification-content"
        v-html="getRenderRichText(notificationInfo.content)"
      ></div>
    </div>

    <!-- 消息通知详情弹窗 -->
    <NotificationDetailDialog
      v-model="showNotificationDetail"
      :notificationDetail="notificationInfo"
    />
  </div>
</template>

<script setup lang="ts">
import { readNotification } from "@/api/canteen/client/notification";
import NotificationDetailDialog from "./nitificationDetailDialog.vue";
import { INotification } from "@/api/canteen/client/types/notification";
import dayjs from "dayjs";
import { withDefaults } from "vue";

/** 外部传入的消息通知信息 */
interface IProps {
  notificationInfo: INotification;
}

/** 组件事件接口 */
interface IEmits {
  /** 消息通知更新后事件（用于刷新父组件数据） */
  (e: "refresh"): void;
}

/** 富文本渲染区域 */
const contentRef = ref<HTMLDivElement | null>(null);

const props = withDefaults(defineProps<IProps>(), {
  notificationInfo: () => ({} as INotification),
});

const emit = defineEmits<IEmits>();

/** 渲染富文本内容 */
function getRenderRichText(htmlContent?: string) {
  if (!htmlContent) return "";

  const ALLOWED_TAGS = [
    "p",
    "br",
    "strong",
    "em",
    "u",
    "ol",
    "ul",
    "li",
    "a",
    "img",
    "h1",
    "h2",
    "h3",
  ];
  const ALLOWED_ATTR = ["href", "src", "alt", "title"];
  // 安全过滤
  const cleanHTML = htmlContent
    .replace(/<[^>]+>/g, (tag) => {
      const tagName = tag.match(/<(\w+)/)![1];
      if (ALLOWED_TAGS.includes(tagName)) {
        return tag;
      }
      return "";
    })
    .replace(/<[^>]+>/g, (tag) => {
      const attrs = tag.match(/\s[^=]+="[^"]*"/g) || [];
      return attrs
        .filter((attr) => ALLOWED_ATTR.includes(attr.split("=")[0].trim()))
        .join("");
    });
  return cleanHTML;
}

/** 是否显示消息通知详情 */
const showNotificationDetail = ref(false);

/** 点击查看详情 */
const handleViewDetail = async () => {
  handleReadNotification();
  showNotificationDetail.value = true;
};

/** 该条消息是否已读 */
const isRead = ref(props.notificationInfo.isRead);

watch(
  () => props.notificationInfo.isRead,
  (newVal) => {
    isRead.value = newVal;
  },
  {
    immediate: true,
  }
);

/** 是否正在提交 */
const submitting = ref(false);

/** 消息已读 */
const handleReadNotification = async () => {
  try {
    if (submitting.value) return;
    submitting.value = true;
    if (isRead.value) return;
    readNotification([props.notificationInfo.id]);
    isRead.value = true;
    return true;
  } catch (error) {
  } finally {
    submitting.value = false;
  }
};
</script>

<style scoped lang="scss">
.notification-card {
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  background-color: #fff;

  display: flex;
  align-items: flex-start;
  column-gap: 10px;

  cursor: pointer;

  .content-wrapper {
    flex: 1;
  }

  .card-header {
    .title-wrapper {
      display: flex;
      align-items: center;
      column-gap: 10px;
      margin-bottom: 8px;

      .title-text {
        font-size: 18px;
        color: #000000;
        line-height: 1;
        margin: 0;
      }
    }
    .publish-time {
      font-size: 14px;
      color: #a6a6a6;
      line-height: 1;
      margin: 0;
    }
  }

  .notification-content {
    padding-top: 16px;
    line-height: 1.5;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    color: #383838;
  }

  .actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    column-gap: 16px;

    .action-btn {
      font-size: 16px;
      transition: all 0.3s ease;

      &.loading {
        opacity: 0.6;
        cursor: not-allowed;
        pointer-events: none;
      }
    }
  }
}
</style>
