package com.hainancrc.module.codeengine.service.common;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.codeengine.configuration.AppConfiguration;
import com.hainancrc.module.codeengine.entity.ApplicationChannelRelDO;
import com.hainancrc.module.codeengine.entity.ApplicationSceneRelDO;
import com.hainancrc.module.codeengine.entity.ApplyDO;
import com.hainancrc.module.codeengine.entity.CodeRecordDO;
import com.hainancrc.module.codeengine.entity.SceneDO;
import com.hainancrc.module.codeengine.enums.ApplyCategory;
import com.hainancrc.module.codeengine.mapper.applicationchannelrel.ApplicationChannelRelMapper;
import com.hainancrc.module.codeengine.mapper.applicationscenerel.ApplicationSceneRelMapper;
import com.hainancrc.module.codeengine.mapper.apply.ApplyMapper;
import com.hainancrc.module.codeengine.mapper.coderecord.CodeRecordMapper;
import com.hainancrc.module.codeengine.mapper.scene.SceneMapper;

import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.*;

import cn.hutool.core.lang.UUID;
import lombok.var;

@Service
public class CodeServiceImpl implements CodeService {

    @Resource
    private AppConfiguration appConfiguration;

    @Resource
    private ApplicationChannelRelMapper applicationChannelRelMapper;

    @Resource
    private ApplicationSceneRelMapper applicationSceneRelMapper;

    @Resource
    private SceneMapper sceneMapper;

    @Resource
    private CodeRecordMapper codeRecordMapper;

    @Resource
    private ApplyMapper applyMapper;

    @Override
    public Boolean applicationRelatedChannel(Long applicationId, Long channelId) {
        return applicationChannelRelMapper.selectOne(
                new LambdaQueryWrapperX<ApplicationChannelRelDO>()
                        .eq(ApplicationChannelRelDO::getApplicationId, applicationId)
                        .eq(ApplicationChannelRelDO::getChannelId, channelId)) != null;
    }

    @Override
    public Boolean applicationRelatedChannels(Long applicationId, Long[] channelIds) {
        return applicationChannelRelMapper.selectList(
                new LambdaQueryWrapperX<ApplicationChannelRelDO>()
                        .eq(ApplicationChannelRelDO::getApplicationId, applicationId)
                        .in(ApplicationChannelRelDO::getChannelId, (Object[]) channelIds))
                .size() == channelIds.length;
    }

    @Override
    public Boolean applicationRelatedScene(Long applicationId, Long sceneId) {
        return applicationSceneRelMapper.selectOne(
                new LambdaQueryWrapperX<ApplicationSceneRelDO>()
                        .eq(ApplicationSceneRelDO::getApplicationId, applicationId)
                        .eq(ApplicationSceneRelDO::getSceneId, sceneId)) != null;
    }

    @Override
    public Boolean applicationRelatedScenes(Long applicationId, Long[] sceneIds) {
        return applicationSceneRelMapper.selectList(
                new LambdaQueryWrapperX<ApplicationSceneRelDO>()
                        .eq(ApplicationSceneRelDO::getApplicationId, applicationId)
                        .in(ApplicationSceneRelDO::getSceneId, (Object[]) sceneIds))
                .size() == sceneIds.length;
    }

    @Override
    public Boolean validateExpired(SceneDO sceneDO, ApplyCategory category, Date expiredDate) {

        Date maxExpiredDate = sceneExpiredTime(sceneDO, category,new Date());
        // 判断测试的 expiredDate 是否在当前最长有效时间内
        return maxExpiredDate == null || expiredDate.before(maxExpiredDate);

    }

    @Override
    public Date sceneExpiredTime(SceneDO sceneDO, ApplyCategory category,Date initDate) {
        if (sceneDO == null) {
            throw exception(SCENE_NOT_EXISTS);
        }

        Date maxExpiredDate = null;
        
        if (category == ApplyCategory.STATIC && sceneDO.getStaticExpiredMonth() != null) {
            // 当前时间加上静态有效自然月获得当前最长有效时间
            Calendar calendar = Calendar.getInstance();
            if(initDate != null) calendar.setTime(initDate);

            calendar.add(Calendar.MONTH, sceneDO.getStaticExpiredMonth());
            maxExpiredDate = calendar.getTime();
        } else if (category == ApplyCategory.DYNAMIC && sceneDO.getDynamicExpiredMinute() != null) {
            // 当前时间加上动态有效分钟数获得当前最长有效时间
            Calendar calendar = Calendar.getInstance();
            if(initDate != null) calendar.setTime(initDate);
            
            calendar.add(Calendar.MINUTE, sceneDO.getDynamicExpiredMinute());
            maxExpiredDate = calendar.getTime();
        }

        return maxExpiredDate;
    }

    private String createOrderId(String prefix, Long id, Integer length) {
        return prefix + String.format("%0" + length + "d", id);
    }

    @Override
    public String createApplyOrderId(Long id) {
        return createOrderId("SM", id, 8);
    }

    @Override
    public String createCodeLogOrderId(Long id) {
        return createOrderId("NO", id, 16);
    }

    @Override
    public CodeRecordDO createCodeRecord(ApplyDO applyDO, String codePrefix,ApplyCategory category) {

        CodeRecordDO codeRecordDO = new CodeRecordDO();
        codeRecordDO.setCodeKey(UUID.randomUUID().toString()); // TODO 可能要更换雪花算法
        codeRecordDO.setApplyId(applyDO.getId());
        codeRecordDO.setScanCount(0);
        codeRecordDO.setApplyCategory(category);
        codeRecordDO.setCodeRecordExpired(false);

        // 生成一个二维码内容
        String prefix = codePrefix == null ? appConfiguration.getQrcodePrefix() : codePrefix;
        if(prefix == null) prefix = "";

        String url = prefix +
                codeRecordDO.getCodeKey() +
                "&e=" +
                String.format("%s-%s-%s-%s",
                        createOrderId("", applyDO.getId(), 8),
                        createOrderId("", applyDO.getApplicationId(), 6),
                        createOrderId("", applyDO.getGeneratedCount().longValue(), 8),
                        Calendar.getInstance().getTimeInMillis()) +
                "&l=" + prefix.length();


        codeRecordDO.setCodeUrl(url);
        codeRecordMapper.insert(codeRecordDO);

        applyDO.setGeneratedCount(applyDO.getGeneratedCount() + 1);
        applyMapper.updateById(applyDO);

        return codeRecordDO;
    }

    @Override
    public String comboChannelIdToStr(Long[] channelIds) {
        if (channelIds == null && channelIds.length == 0){
            return null;
        }
        return Arrays.stream(channelIds).distinct().map(m -> String.valueOf(m)).collect(Collectors.joining(","));
    }



}
