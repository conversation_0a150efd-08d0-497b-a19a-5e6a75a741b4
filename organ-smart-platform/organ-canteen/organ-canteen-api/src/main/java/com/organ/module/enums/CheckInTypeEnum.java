package com.organ.module.enums;

import lombok.Getter;

/**
 * 核销类型枚举
 */
@Getter
public enum CheckInTypeEnum {

    NORMAL("NORMAL", "正常核销"),
    SPECIAL("SPECIAL", "特殊核销"),
    REPLACEMENT("REPLACEMENT", "补录核销");

    private final String code;
    private final String description;

    CheckInTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
}
