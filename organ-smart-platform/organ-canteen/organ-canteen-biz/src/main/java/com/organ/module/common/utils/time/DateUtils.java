package com.organ.module.common.utils.time;

import java.time.LocalTime;

public class DateUtils {

    /**
     * 判断一个 Date 是否在 LocalDate 区间内（包含边界）
     */
    public static boolean isIn(LocalTime date, LocalTime startDate, LocalTime endDate) {
        if (date == null || startDate == null || endDate == null) {
            return false;
        }

        return !date.isBefore(startDate) && date.isBefore(endDate);
    }
}
