package com.hainancrc.module.codeengine.api.scene.vo;

import lombok.*;
import java.util.*;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.*;
import com.hainancrc.module.codeengine.api.scene.dto.*;

@ApiModel("场景 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SceneRespVO extends SceneBaseDTO {
    
    @ApiModelProperty(value = "场景ID", required = true)
    @NotNull(message = "场景ID不能为空")
    private Long id;

    @ApiModelProperty(value = "场景Code")
    private String sceneCode;

    @ApiModelProperty(value = "数据源Id")
    private Long[] dataSourceId;

    @ApiModelProperty(value = "应用Id")
    private Long[] applicationId;

    @ApiModelProperty(value = "创建时间", required = true)
    private Date createTime;

}
