import service from "@/service";
import { BIZ_MODULE_API_PREFIX } from "@/api/canteen/constant";
import { IDailyDiningBoard } from "./types/stastistic";

/** 模块API前缀 */
const MODULE_API_PREFIX = `${BIZ_MODULE_API_PREFIX}/mealPeriod`;

/** 是否模拟数据 */
const isMock = true;

/** 获取当日就餐看板 */
export function getTodayDiningBoard() {
  if (isMock) {
    return new Promise<IDailyDiningBoard>((resolve) => {
      resolve({
        /** 日期 */
        date: new Date().toISOString(),
        /** 用餐总人数 */
        totalDiners: 1234,
        /** 临时码剩余数量 */
        remainingTemporaryCodes: 50,
        /** 预约总数 */
        totalBookings: 12345,
      });
    });
  }
  return service.get<any, any>(`${MODULE_API_PREFIX}/todayDiningBoard`, {});
}
