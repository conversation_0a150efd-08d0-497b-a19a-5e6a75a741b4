package com.organ.module.canteen.booking.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import com.organ.module.enums.BookingStatusEnum;
import com.organ.module.enums.BookingTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.Date;

@Data
@TableName("canteen_booking")
@EqualsAndHashCode(callSuper = true)
public class BookingDO extends BaseDO {

    @TableId
    private Long id;

    /**
     * 餐次id
     */
    private Long mealPeriodId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 预约类型
     */
    private BookingTypeEnum bookingType;

    /**
     * 预约状态
     */
    private BookingStatusEnum bookingStatus;

    /**
     * 预约时间
     */
    private LocalDate mealDate;

    /**
     * 预约码
     */
    private String bookingCode;

    /**
     * 预约备注
     */
    private String remark;
}
