package com.organ.module.canteen.reconciliation.service;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.common.pojo.PageResult;
import com.organ.module.canteen.api.reconciliation.dto.ReconciliationPageDTO;
import com.organ.module.canteen.api.reconciliation.vo.ReconciliationPageRespVO;
import com.organ.module.canteen.api.reconciliation.vo.ReconciliationStatVO;
import com.organ.module.canteen.mealperiod.entity.MealPeriodDO;
import com.organ.module.canteen.mealperiod.mapper.MealPeriodMapper;
import com.organ.module.canteen.reconciliation.mapper.ReconciliationMapper;
import com.organ.module.common.utils.time.DateUtils;
import com.organ.module.common.utils.time.TimeRange;
import com.organ.module.enums.ReconciliationTypeEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Service
public class ReconciliationServiceImpl implements ReconciliationService {


    @Resource
    private MealPeriodMapper mealPeriodMapper;

    @Resource
    private ReconciliationMapper reconciliationMapper;

    @Override
    public ReconciliationStatVO getStatisticsInfo(Long canteenId, ReconciliationTypeEnum type) {
        if (canteenId == null || type == null) {
            return new ReconciliationStatVO();
        }

        LocalDateTime currentTime = LocalDateTime.now();
        LocalDate currentDate = currentTime.toLocalDate();
        LocalTime currentLocalTime = currentTime.toLocalTime();

        switch (type) {
            case MEAL:
                MealPeriodDO targetPeriod = getTargetMealPeriod(canteenId, currentLocalTime);
                if (targetPeriod != null) {
                    return reconciliationMapper.getReconciliationStatByMeal(
                            canteenId, targetPeriod.getId(), currentDate.atStartOfDay());
                }
                return new ReconciliationStatVO();
            case DAY:
                return reconciliationMapper.getReconciliationStatByRange(
                        canteenId, TimeRange.ofDay(currentDate));
            case MONTH:
                return reconciliationMapper.getReconciliationStatByRange(
                        canteenId, TimeRange.ofMonth(currentDate));
            default:
                return new ReconciliationStatVO();
        }
    }

    @Override
    public PageResult<ReconciliationPageRespVO> getReconciliationPage(ReconciliationPageDTO pageDTO) {
        if (pageDTO.getCanteenId() == null || pageDTO.getType() == null) {
            return PageResult.empty();
        }

        Page<ReconciliationPageRespVO> page = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDate currentDate = currentTime.toLocalDate();
        LocalTime currentLocalTime = currentTime.toLocalTime();

        Page<ReconciliationPageRespVO> pageResult;
        switch (pageDTO.getType()) {
            case MEAL:
                MealPeriodDO targetPeriod = getTargetMealPeriod(pageDTO.getCanteenId(), currentLocalTime);
                if (targetPeriod != null) {
                    pageResult = reconciliationMapper.getReconciliationPageByMeal(
                            page, pageDTO, targetPeriod.getId(), currentDate.atStartOfDay());
                } else {
                    pageResult = new Page<>();
                }
                break;
            case DAY:
                pageResult = reconciliationMapper.getReconciliationPageByRange(
                        page, pageDTO, TimeRange.ofDay(currentDate));
                break;
            case MONTH:
                pageResult = reconciliationMapper.getReconciliationPageByRange(
                        page, pageDTO, TimeRange.ofMonth(currentDate));
                break;
            default:
                pageResult = new Page<>();
                break;
        }

        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    /**
     * 获取目标餐次时间段
     * @param canteenId 餐厅ID
     * @param currentLocalTime 当前时间
     * @return 目标餐次时间段
     */
    private MealPeriodDO getTargetMealPeriod(Long canteenId, LocalTime currentLocalTime) {
        List<MealPeriodDO> periodList = mealPeriodMapper.getMealPeriodByCanteenId(canteenId);
        if (ObjectUtil.isEmpty(periodList)) {
            return null;
        }

        MealPeriodDO currentPeriod = null;
        MealPeriodDO lastPastPeriod = null;

        for (MealPeriodDO period : periodList) {
            if (DateUtils.isIn(currentLocalTime, period.getStartTime(), period.getEndTime())) {
                currentPeriod = period;
            } else if (period.getEndTime().isBefore(currentLocalTime)) {
                if (lastPastPeriod == null || period.getEndTime().isAfter(lastPastPeriod.getEndTime())) {
                    lastPastPeriod = period;
                }
            }
        }

        return currentPeriod != null ? currentPeriod : lastPastPeriod;
    }

}
