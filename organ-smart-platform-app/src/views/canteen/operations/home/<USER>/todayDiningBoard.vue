<template>
  <div class="today-dining-board">
    <div class="header-wrapper mb-10">
      <div class="icon-wrapper">
        <img class="img-icon" src="@/assets/images/canteen/board.png" />
      </div>
      <div class="content">
        <div class="title">当日就餐看板</div>
        <div class="mb-10">查看就餐数据统计</div>
      </div>
    </div>
    <div class="statistic-wrappper">
      <div
        v-for="item in stastistics"
        :key="item.label"
        class="statistic-item"
        :style="item.style"
      >
        <div class="value">{{ item.value }}</div>
        <div class="label">{{ item.label }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IDailyDiningBoard } from "@/api/canteen/client/types/stastistic";
import { formatNumberWithCommas } from "@/utils/number";

const props = withDefaults(
  defineProps<{
    todayDiningBoard: IDailyDiningBoard;
  }>(),
  {
    todayDiningBoard: () =>
      ({
        totalDiners: 0,
        remainingTemporaryCodes: 0,
        totalBookings: 0,
      } as IDailyDiningBoard),
  }
);

/** 看板统计项 */
const stastistics = computed(() => {
  return [
    {
      label: "就餐总数（人）",
      value: formatNumberWithCommas(props.todayDiningBoard.totalDiners),
      style: {
        color: "#22C55E",
        backgroundColor: "#F0FDF4",
      },
    },
    {
      label: "临时码剩余数量（个）",
      value: formatNumberWithCommas(
        props.todayDiningBoard.remainingTemporaryCodes
      ),
      style: {
        color: "#F5A629",
        backgroundColor: "#FFF7EB",
      },
    },
  ];
});
</script>

<style scoped lang="scss">
$imgIconH: 36px;

.today-dining-board {
  background: #ffffff;
  border-radius: 8px;
  padding: 20px 12px;
  cursor: pointer;

  .header-wrapper {
    display: flex;
    align-items: flex-start;
  }

  .icon-wrapper {
    width: $imgIconH;
    height: $imgIconH;
    margin-right: 12px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border-radius: 10px;
    background-color: #f0fdf4;

    .img-icon {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .content {
    color: #808080;

    .title {
      height: $imgIconH;
      line-height: $imgIconH;
      font-size: 20px;
      color: #000000;
      margin-bottom: 10px;
    }
  }

  .statistic-wrappper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 12px;

    .statistic-item {
      padding: 16px;
      width: calc(50% - 6px);

      .value {
        font-size: 20px;
        font-weight: 500;
        margin-bottom: 6px;
      }

      .label {
        font-size: 14px;
        color: #808080;
      }
    }
  }

  .mb-10 {
    margin-bottom: 10px;
  }
}
</style>
