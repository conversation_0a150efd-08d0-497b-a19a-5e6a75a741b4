package com.hainancrc.module.codeengine.api.publicapi.vo;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hainancrc.module.codeengine.enums.ApplyCategory;
import com.hainancrc.module.codeengine.enums.CodeLogStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("DecodeStatusRespVO")
@Data
public class DecodeStatusRespVO {

    @ApiModelProperty(value = "解码码状态(authRequired/fail/success)", required = true)
    private CodeLogStatus decodeStatus;

    @ApiModelProperty(value = "码类型",required = true)
    private ApplyCategory codeCategory;


    @ApiModelProperty(value = "渠道code", required = true)
    private String[] channelCodes;
    
    @ApiModelProperty(value = "场景code", required = true)
    private String sceneCode;

    @ApiModelProperty(value = "场景name", required = true)
    private String sceneName;

    @ApiModelProperty(value = "主体ID", required = true)
    private String entityId;

    @ApiModelProperty(value = "主体", required = true)
    private String entity;

    @ApiModelProperty(value = "过期时间", required = true)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date expiredTime;

    @ApiModelProperty(value = "是否过期", required = true)
    private Boolean expired;

    @ApiModelProperty(value = "剩余使用次数", required = true)
    private Integer remainingTimes;


    @ApiModelProperty(value = "数据源数据", required = false)
    private Map<String,Object> dataSourceData;

    @ApiModelProperty(value = "额外参数", required = false)
    private String extraParameters;
}