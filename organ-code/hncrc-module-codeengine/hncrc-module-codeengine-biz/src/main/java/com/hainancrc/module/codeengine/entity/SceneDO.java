package com.hainancrc.module.codeengine.entity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 场景 DO
 *
 * <AUTHOR>
 */
@TableName("codeengine_scene")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SceneDO extends BaseDO {

    /**
     * 场景ID
     */
    @TableId
    private Long id;
    /**
     * 场景code
     */
    private String sceneCode;
    /**
     * 场景名称
     */
    private String sceneName;
    /**
     * 场景描述
     */
    private String sceneDescription;
    /**
     * 场景码Logo
     */
    private String sceneCodeLogo;
    /**
     * 申码授权ID
     */
    private String applyAuthId;
    /**
     * 亮码授权ID
     */
    private String generateAuthId;
    /**
     * 解码授权ID
     */
    private String decodeAuthId;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 静态规则过期月数(null为永久有效)
     */
    private Integer staticExpiredMonth;
    /**
     * 动态规则过期次数
     */
    private Integer dynamicExpiredTimes;
    /**
     * 动态规则过期分钟数
     */
    private Integer dynamicExpiredMinute;
    /**
     * 是否启用
     */
    private Boolean sceneEnable;

    /**
     * 提交人
     */
    private String presenter;

    /**
     * 额外参数
     */
    private String extraParameters;

}
