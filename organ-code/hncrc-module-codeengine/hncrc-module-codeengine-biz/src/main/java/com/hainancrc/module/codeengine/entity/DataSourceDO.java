package com.hainancrc.module.codeengine.entity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import com.hainancrc.module.codeengine.enums.DataSourceCategory;
import com.hainancrc.module.codeengine.enums.DataSourceHttpMethod;
import com.hainancrc.module.codeengine.enums.DataSourceStatus;

/**
 * 数据源 DO
 *
 * <AUTHOR>
 */
@TableName("codeengine_data_source")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataSourceDO extends BaseDO {

    /**
     * 数据源ID
     */
    @TableId
    private Long id;
    /**
     * 数据源名称
     */
    private String dataSourceName;
    /**
     * 数据源 code
     */
    private String dataSourceCode;
    /**
     * 负责人
     */
    private String dataSourceOwner;

    /**
     * 数据源链接状态(REACHABLE/UNREACHABLE)
     */
    private DataSourceStatus dataSourceStatus;
    
    /**
     * 数据源描述
     */
    private String dataSourceDescription;
    /**
     * 数据源分类(API/OTHER)
     */
    private DataSourceCategory dataSourceCategory;
    /**
     * 数据源API地址
     */
    private String dataSourceUrl;
    /**
     * 数据源API请求方式(GET/POST/PUT/DELETE)
     */
    private DataSourceHttpMethod dataSourceMethod;
    /**
     * 数据源API请求参数
     */
    private String queryParameters;
    /**
     * 数据源API请求头
     */
    private String headerParameters;

}
