package com.organ.module.canteen.api.reconciliation.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReconciliationStatVO {

    @ApiModelProperty(value = "总预约数")
    private Long totalReservationCount = 0L;

    @ApiModelProperty(value = "已使用订单")
    private Long usedCount = 0L;

    @ApiModelProperty(value = "待使用订单")
    private Long pendingCount = 0L;

    @ApiModelProperty(value = "未就餐订单")
    private Long unconsumedCount = 0L;

}
