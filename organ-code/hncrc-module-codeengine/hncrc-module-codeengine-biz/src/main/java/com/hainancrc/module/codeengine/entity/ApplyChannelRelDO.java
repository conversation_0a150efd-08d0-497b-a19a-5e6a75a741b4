package com.hainancrc.module.codeengine.entity;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 申码渠道多对多 DO
 *
 * <AUTHOR>
 */
@TableName("codeengine_apply_channel_rel")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplyChannelRelDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 申码记录ID
     */
    private Long applyId;
    /**
     * 渠道ID
     */
    private Long channelId;

}
