package  com.hainancrc.module.codeengine.api.publicapi.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hainancrc.module.codeengine.enums.ApplyCategory;
import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;
import java.util.Date;

@ApiModel("亮码记录创建 Request VO")
@Data
public class GenerateCreateDTO {

    @ApiModelProperty(value = "应用Code", required = true)
    @NotNull(message = "应用Code不能为空")
    private String application;
    
    @ApiModelProperty(value = "申码订单号", required = true)
    @NotEmpty(message = "申码订单号不能为空")
    private String applyOrderId;

    @ApiModelProperty(value = "渠道Code", required = true)
    @NotNull(message = "渠道Code不能为空")
    private String channel;

    @ApiModelProperty(value = "申码分类(dynamic/static)", required = true)
    @NotNull(message = "申码分类(dynamic/static)不能为空")
    private ApplyCategory category;

}
