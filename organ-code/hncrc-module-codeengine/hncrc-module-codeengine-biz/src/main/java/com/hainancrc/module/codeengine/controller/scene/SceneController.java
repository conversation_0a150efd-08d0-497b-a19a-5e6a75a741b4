package com.hainancrc.module.codeengine.controller.scene;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.annotations.*;

import javax.validation.*;
import java.util.*;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.common.pojo.CommonResult;
import static com.hainancrc.framework.common.pojo.CommonResult.success;

import com.hainancrc.module.codeengine.api.common.IdNameVO;
import com.hainancrc.module.codeengine.api.scene.dto.*;
import com.hainancrc.module.codeengine.api.scene.vo.*;
import com.hainancrc.module.codeengine.entity.SceneDO;
import com.hainancrc.module.codeengine.convert.scene.SceneConvert;
import com.hainancrc.module.codeengine.service.scene.SceneService;

@Api(tags = "场景")
@RestController
@RequestMapping("/codeengine/scene")
@Validated
public class SceneController {

    @Resource
    private SceneService sceneService;

    @GetMapping("/keyValue")
    @ApiOperation("以 key-value 方式获取渠道内容")
    @ApiImplicitParam(name = "status",
            value = "启用状态,0 禁用 1 启用 2 全部 不传默认 启用", required = false, dataTypeClass = Integer.class)
    public CommonResult<List<IdNameVO>> getKeyValue(@RequestParam(name="status",required = false) Integer status) {
        return success(sceneService.getKeyValue(status));
    }

    @PostMapping("/create")
    @ApiOperation("创建场景")
    public CommonResult<Long> createScene(@Valid @RequestBody SceneCreateDTO createDTO) {
        return success(sceneService.createScene(createDTO));
    }

    @PutMapping("/update")
    @ApiOperation("更新场景")
    public CommonResult<Boolean> updateScene(@Valid @RequestBody SceneUpdateDTO updateDTO) {
        sceneService.updateScene(updateDTO);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得场景")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<SceneRespVO> getScene(@RequestParam("id") Long id) {
        return success(sceneService.getScene(id));
    }

    @GetMapping("/list")
    @ApiOperation("获得场景列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    public CommonResult<List<SceneRespVO>> getSceneList(@RequestParam("ids") Collection<Long> ids) {
        List<SceneDO> list = sceneService.getSceneList(ids);
        return success(SceneConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得场景分页")
    public CommonResult<PageResult<SceneRespVO>> getScenePage(@Valid ScenePageDTO pageDTO) {
        PageResult<SceneDO> pageResult = sceneService.getScenePage(pageDTO);
        return success(SceneConvert.INSTANCE.convertPage(pageResult));
    }


    @PutMapping("/enable")
    @ApiOperation("启用场景")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> enableApplication(@RequestParam("id") Long id) {
        sceneService.enableScene(id);
        return success(true);
    }

    @PutMapping("/disable")
    @ApiOperation("禁用场景")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> disableApplication(@RequestParam("id") Long id) {
        sceneService.disableScene(id);
        return success(true);
    }

    @PostMapping("/relateApplication")
    @ApiOperation("关联应用")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class),
        @ApiImplicitParam(name = "applicationIds", value = "应用编号", required = true, dataTypeClass = String.class)
    })
    public CommonResult<Boolean> relateApplication(@RequestParam("id") Long id, @RequestParam("applicationIds") String applicationIds) {
        sceneService.relateApplication(id,applicationIds);
        return success(true);
    }

    @PostMapping("/relateDataSource")
    @ApiOperation("关联数据源")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class),
        @ApiImplicitParam(name = "dataSourceIds", value = "数据源编号", required = true, dataTypeClass = String.class)
    })
    public CommonResult<Boolean> relateDataSource(@RequestParam("id") Long id, @RequestParam("dataSourceIds") String dataSourceIds) {
        sceneService.relateDataSource(id,dataSourceIds);
        return success(true);
    }

}
