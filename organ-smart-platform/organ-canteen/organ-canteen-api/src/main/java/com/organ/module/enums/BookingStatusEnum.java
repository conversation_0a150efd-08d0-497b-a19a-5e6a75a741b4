package com.organ.module.enums;

import lombok.Getter;

/**
 * 预约状态枚举
 */
@Getter
public enum BookingStatusEnum {

    BOOKED("BOOKED", "待使用"),
    CANCELED("CANCELED", "已取消"),
    VERIFIED("VERIFIED", "已核销"),
    EXPIRED("EXPIRED", "已失效");

    private final String code;
    private final String description;

    BookingStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
}
