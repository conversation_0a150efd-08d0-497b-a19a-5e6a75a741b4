package com.hainancrc.module.codeengine.api.datasource.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import com.hainancrc.framework.common.pojo.PageParam;
import com.hainancrc.module.codeengine.enums.DataSourceStatus;

import org.springframework.format.annotation.DateTimeFormat;


@ApiModel("数据源分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DataSourcePageDTO extends PageParam {


    @ApiModelProperty(value = "数据源名称")
    private String dataSourceName;

    @ApiModelProperty(value = "负责人")
    private String dataSourceOwner;

    @ApiModelProperty(value = "数据源状态(REACHABLE/UNREACHABLE)")
    private DataSourceStatus dataSourceStatus;

    @ApiModelProperty(value = "创建时间开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeStart;

    @ApiModelProperty(value = "创建时间结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTimeEnd;
}
