package com.hainancrc.module.codeengine.api.codelog;

import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.codeengine.api.codelog.dto.CodeLogPageDTO;
import com.hainancrc.module.codeengine.api.codelog.dto.CodeLogQueryDTO;
import com.hainancrc.module.codeengine.api.codelog.vo.CodeLogRespVO;
import com.hainancrc.module.codeengine.api.codelog.vo.SimpleCodeLogRespVO;
import com.hainancrc.module.codeengine.api.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/01/18 10:30
 **/
@FeignClient(name = ApiConstants.NAME,contextId = "CODE-LOG-API")
public interface CodeLogApi {


    @PostMapping("/code/log/query")
    PageResult<SimpleCodeLogRespVO> queryLog(@RequestBody @Valid CodeLogQueryDTO pageDTO);

}
