package com.hainancrc.organgateway.jackson.config;

import java.time.LocalDateTime;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.hainancrc.framework.common.utils.json.JsonUtils;
import com.hainancrc.organgateway.jackson.databind.LocalDateTimeDeserializer;
import com.hainancrc.organgateway.jackson.databind.LocalDateTimeSerializer;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Configuration
public class JacksonAutoConfiguration {

    @Bean
    public BeanPostProcessor objectMapperBeanPostProcessor() {
        return new BeanPostProcessor() {
            @Override
            public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
                if (!(bean instanceof ObjectMapper)) {
                    return bean;
                }
                ObjectMapper objectMapper = (ObjectMapper) bean;
                SimpleModule simpleModule = new SimpleModule();
                /*
                 * Long类型序列化规则，数值超过2^53-1，在JS会出现精度丢失问题，因此Long自动序列化为字符串类型
                 * LocalDateTime序列化、反序列化规则
                 */
                simpleModule
                        // .addSerializer(Long.class, ToStringSerializer.instance)
                        // .addSerializer(Long.TYPE, ToStringSerializer.instance)
                        .addSerializer(LocalDateTime.class, LocalDateTimeSerializer.INSTANCE)
                        .addDeserializer(LocalDateTime.class, LocalDateTimeDeserializer.INSTANCE);

                objectMapper.registerModules(simpleModule);

                JsonUtils.init(objectMapper);
                log.debug("初始化 jackson 自动配置");
                return bean;
            }
        };
    }

}
