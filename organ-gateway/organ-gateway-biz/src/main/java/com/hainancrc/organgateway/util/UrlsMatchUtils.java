package com.hainancrc.organgateway.util;

import java.util.List;

import org.springframework.util.AntPathMatcher;

/**
 * 地址匹配工具栏
 * 支持 *，**，?
 */
public class UrlsMatchUtils {

    private static final AntPathMatcher pathMatcher = new AntPathMatcher();

    public static Boolean matchUrl(List<String> urlList, String path) {
        for (String url : urlList) {
            if (pathMatcher.match(url, path)) {
                return true;
            }
        }
        return false;
    }

}
