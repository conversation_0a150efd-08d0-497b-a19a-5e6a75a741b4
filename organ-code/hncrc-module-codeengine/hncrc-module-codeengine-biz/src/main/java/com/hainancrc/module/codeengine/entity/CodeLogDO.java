package com.hainancrc.module.codeengine.entity;

import com.hainancrc.module.codeengine.enums.ApplyCategory;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import com.hainancrc.module.codeengine.enums.CodeLogCategory;
import com.hainancrc.module.codeengine.enums.CodeLogStatus;

import java.util.Date;

/**
 * 生解码操作记录 DO
 *
 * <AUTHOR>
 */
@TableName("codeengine_code_log")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CodeLogDO extends BaseDO {

    /**
     * 记录ID
     */
    @TableId
    private Long id;
    /**
     * 操作订单号
     */
    private String codeLogOrderId;
    /**
     * 操作类型(apply/generate/decode)
     */
    private CodeLogCategory codeLogCategory;
    /**
     * 授权场景ID
     */
    private String codeLogAuthId;
    /**
     * 授权场景订单号
     */
    private String codeLogAuthOrderId;
    /**
     * 申码ID
     */
    private Long codeLogApplyId;
    /**
     * 二维码key
     */
    private String codeKey;
    /**
     * 操作状态(authRequired/fail/success)
     */
    private CodeLogStatus logStatus;

    /**
     * 应用ID
     */
    private Long applicationId;
    /**
     * 渠道ID
     */
    private String channelIdStr;

    private String channelCode;
    /**
     * 场景ID
     */
    private Long sceneId;
    /**
     * 主体 id
     */
    private String entityId;
    /**
     * 主体
     */
    private String entity;
    /**
     * 请求主体 id
     */
    private String requestEntityId;
    /**
     * 请求主体
     */
    private String requestEntity;

    private Date validityTime;

    private ApplyCategory codeCategory;

    private String CreateName;

}
