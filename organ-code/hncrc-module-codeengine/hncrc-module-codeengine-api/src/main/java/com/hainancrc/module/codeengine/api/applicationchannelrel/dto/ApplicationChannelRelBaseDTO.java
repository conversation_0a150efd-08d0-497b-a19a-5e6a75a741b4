package com.hainancrc.module.codeengine.api.applicationchannelrel.dto;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 应用渠道多对多
*/
@Data
public class ApplicationChannelRelBaseDTO {

    @ApiModelProperty(value = "应用ID", required = true)
    @NotNull(message = "应用ID不能为空")
    private Integer applicationId;

    @ApiModelProperty(value = "渠道ID", required = true)
    @NotNull(message = "渠道ID不能为空")
    private Integer channelId;

}
