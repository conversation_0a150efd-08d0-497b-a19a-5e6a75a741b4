import { createRouterFlatModule, createRouterModule } from "@/router/util";

/** 智慧食堂用户端路由前缀 */
export const moduleName = "client";

/** 用户端路由配置 */
export default createRouterModule({
  name: moduleName,
  options: {
    meta: {
      menu: false,
    },
  },
  children: [
    {
      path: "",
      meta: {
        title: "首页",
      },
      redirect: "home",
    },
    {
      path: "index",
      meta: {
        title: "首页",
      },
      redirect: "home",
    },
    {
      path: "home",
      meta: {
        title: "首页",
      },
      component: () => import("@/views/canteen/client/home/<USER>"),
    },
    ...createRouterFlatModule({
      name: "home",
      children: [
        {
          path: "booking",
          component: () => import("@/views/canteen/client/home/<USER>"),
          meta: {
            title: "预约中心",
          },
        },
      ],
    }),
    {
      path: "diningCode",
      meta: {
        title: "就餐码",
      },
      component: () => import("@/views/canteen/client/diningCode/index.vue"),
    },
    {
      path: "personalCenter",
      meta: {
        title: "个人中心",
      },
      component: () =>
        import("@/views/canteen/client/personalCenter/index.vue"),
    },
    ...createRouterFlatModule({
      name: "personalCenter",
      children: [
        {
          path: "myBooking",
          component: () =>
            import("@/views/canteen/client/personalCenter/myBooking.vue"),
          meta: {
            title: "我的预约",
          },
        },
        {
          path: "notifications",
          component: () =>
            import("@/views/canteen/client/personalCenter/notifications.vue"),
          meta: {
            title: "消息中心",
          },
        },
      ],
    }),
  ],
});
