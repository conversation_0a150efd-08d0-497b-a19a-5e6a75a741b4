package com.hainancrc.module.codeengine.api.datasource.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

import com.hainancrc.module.codeengine.enums.DataSourceHttpMethod;
import com.hainancrc.module.codeengine.enums.DataSourceStatus;

/**
* 数据源
*/
@Data
public class DataSourceBaseDTO {

    @ApiModelProperty(value = "数据源code", required = true)
    @NotNull(message = "数据源code不能为空")
    private String dataSourceCode;

    @ApiModelProperty(value = "数据源名称", required = true)
    @NotNull(message = "数据源名称不能为空")
    private String dataSourceName;

    @ApiModelProperty(value = "数据源描述")
    private String dataSourceDescription;

    @ApiModelProperty(value = "数据源链接状态(REACHABLE/UNREACHABLE)", required = true)
    private DataSourceStatus dataSourceStatus;

    @ApiModelProperty(value = "负责人")
    @NotEmpty(message = "负责人不能为空")
    private String dataSourceOwner;

    @ApiModelProperty(value = "数据源分类(API/OTHER)", required = true)
    @NotNull(message = "数据源分类(API/OTHER)不能为空")
    private String dataSourceCategory;

    @ApiModelProperty(value = "数据源API地址", required = true)
    @NotNull(message = "数据源API地址不能为空")
    private String dataSourceUrl;

    @ApiModelProperty(value = "数据源API请求方式(GET/POST/PUT/DELETE)", required = true)
    @NotNull(message = "数据源API请求方式(GET/POST/PUT/DELETE)不能为空")
    private DataSourceHttpMethod dataSourceMethod;

    @ApiModelProperty(value = "数据源API请求参数")
    private String queryParameters;

    @ApiModelProperty(value = "数据源API请求头")
    private String headerParameters;

}
