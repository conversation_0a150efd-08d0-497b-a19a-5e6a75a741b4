package com.hainancrc.module.codeengine.convert.datasource;

import java.util.*;

import com.hainancrc.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.hainancrc.module.codeengine.api.datasource.dto.*;
import com.hainancrc.module.codeengine.api.datasource.vo.*;
import com.hainancrc.module.codeengine.entity.DataSourceDO;

/**
 * 数据源 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface DataSourceConvert {

    DataSourceConvert INSTANCE = Mappers.getMapper(DataSourceConvert.class);

    DataSourceDO convert(DataSourceCreateDTO bean);

    DataSourceDO convert(DataSourceUpdateDTO bean);

    DataSourceRespVO convert(DataSourceDO bean);

    List<DataSourceRespVO> convertList(List<DataSourceDO> list);

    PageResult<DataSourceRespVO> convertPage(PageResult<DataSourceDO> page);


}
