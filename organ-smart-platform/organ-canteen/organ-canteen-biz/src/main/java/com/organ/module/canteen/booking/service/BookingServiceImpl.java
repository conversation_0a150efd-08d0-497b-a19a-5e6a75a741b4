package com.organ.module.canteen.booking.service;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hainancrc.framework.common.exception.ServiceException;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.security.core.holder.SysUserHolder;
import com.organ.module.canteen.api.booking.dto.BookingCreateDTO;
import com.organ.module.canteen.api.booking.dto.BookingCreateDetailDTO;
import com.organ.module.canteen.api.booking.dto.BookingPageDTO;
import com.organ.module.canteen.api.booking.vo.ReserveFailedRespVO;
import com.organ.module.canteen.api.booking.vo.ReserveRespVO;
import com.organ.module.canteen.booking.entity.BookingDO;
import com.organ.module.canteen.booking.mapper.BookingMapper;
import com.organ.module.canteen.api.booking.vo.BookingPageRespVO;
import com.organ.module.canteen.api.booking.vo.BookingRespVO;
import com.organ.module.canteen.checkinrecord.entity.CheckInRecordDO;
import com.organ.module.canteen.checkinrecord.mapper.CheckInRecordMapper;
import com.organ.module.canteen.mealperiod.entity.MealPeriodDO;
import com.organ.module.canteen.mealperiod.mapper.MealPeriodMapper;
import com.organ.module.canteen.mealquota.mapper.MealQuotaMapper;
import com.organ.module.enums.BookingStatusEnum;
import com.organ.module.enums.BookingTypeEnum;
import com.organ.module.enums.CheckInSourceEnum;
import com.organ.module.enums.CheckInTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BookingServiceImpl implements BookingService {

    @Resource
    private MealPeriodMapper mealPeriodMapper;

    @Resource
    private MealQuotaMapper mealQuotaMapper;

    @Resource
    private BookingMapper bookingMapper;

    @Resource
    private CheckInRecordMapper checkInRecordMapper;

    @Resource
    private ReserveService reserveService;

    private void validate(Long id) {
        if (bookingMapper.selectById(id) == null) {
            throw new ServiceException(-1, "该预约信息不存在");
        }
    }

    @Override
    public PageResult<BookingPageRespVO> selectBookingPage(BookingPageDTO pageDTO) {
        Page<BookingPageRespVO> page = new Page<>(pageDTO.getPageNo(), pageDTO.getPageSize());
        Page<BookingPageRespVO> pageResult = bookingMapper.selectBookingPage(page, pageDTO);
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }

    @Override
    public BookingRespVO selectBookingById(Long id) {
        return bookingMapper.selectBookingById(id);
    }

    /**
     * 预约(允许失败情况)
     * @param createDTO createDTO
     * @return  Boolean
     */
    @Override
    public ReserveRespVO reserve(BookingCreateDTO createDTO) {

        if (ObjectUtil.isEmpty(createDTO.getDetailList())) {
            throw new ServiceException(-1, "请选择预约餐次信息");
        }

        ReserveRespVO reserveRespVO = new ReserveRespVO();
        reserveRespVO.setCanteenId(createDTO.getCanteenId());

        // 获取食堂可用餐次信息
        List<MealPeriodDO> mealPeriodList = mealPeriodMapper.getAvailableMealPeriodByCanteenId(createDTO.getCanteenId());
        Map<Long, MealPeriodDO> mealPeriodMap = mealPeriodList.stream().collect(Collectors
                .toMap(MealPeriodDO::getId, meal -> meal,
                        (existing, replacement) -> existing));

         LocalTime currentTime = LocalTime.now();

        for (BookingCreateDetailDTO detailDTO : createDTO.getDetailList()) {
            if (ObjectUtil.isEmpty(detailDTO.getMealPeriodIdList())) {
                continue;
            }

            for (Long mealPeriodId : detailDTO.getMealPeriodIdList()) {
                MealPeriodDO mealPeriod = mealPeriodMap.get(mealPeriodId);

                if (mealPeriod == null) {
                    log.info("预约失败 >> 不存在的餐次信息 >> {}", mealPeriodId);
                    reserveRespVO.addFailedReserve(new ReserveFailedRespVO(detailDTO.getReserveDate(), mealPeriodId));
                    continue;
                }

                Boolean reserveResult = reserveService.doReserve(currentTime, detailDTO.getReserveDate(), mealPeriod);
                if (reserveResult) {
                    reserveRespVO.incrementSuccessCount();
                } else {
                    reserveRespVO.incrementFailedCount();
                    reserveRespVO.addFailedReserve(new ReserveFailedRespVO(detailDTO.getReserveDate(), mealPeriod.getId()));
                }
            }

        }

        return reserveRespVO;
    }

    @Override
    public Boolean cancel(Long id) {
        BookingDO bookingDO = bookingMapper.selectById(id);
        if (bookingDO == null) {
            throw new ServiceException(-1, "该预约信息不存在");
        }

        MealPeriodDO mealPeriodDO = mealPeriodMapper.selectById(bookingDO.getMealPeriodId());
        if (mealPeriodDO == null) {
            throw new ServiceException(-1, "该预约信息对应的餐次信息不存在");
        }

        LocalTime now = LocalTime.now();
        if (bookingDO.getBookingType().equals(BookingTypeEnum.ADVANCED)) {
            if (now.isAfter(mealPeriodDO.getAdvanceCancelCloseTime())) {
                throw new ServiceException(-1, "该预约信息已超过预约取消时间，无法取消预约");
            }
        }

        if (bookingDO.getBookingType().equals(BookingTypeEnum.TEMPORARY)) {
            if (now.isAfter(mealPeriodDO.getTempCancelCloseTime())) {
                throw new ServiceException(-1, "该预约信息已超过预约取消时间，无法取消预约");
            }
        }

        return bookingMapper.cancel(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean manualWriteOff(Long id) {

        BookingDO bookingDO = bookingMapper.selectById(id);
        if (bookingDO == null) {
            throw new ServiceException(-1, "该预约信息不存在");
        }

        if (!bookingDO.getBookingStatus().equals(BookingStatusEnum.BOOKED)) {
            throw new ServiceException(-1, "该预约信息处于" + bookingDO.getBookingStatus().getDescription() + "状态，无法进行核销");
        }

        CheckInRecordDO checkInRecord = new CheckInRecordDO();
        checkInRecord.setBookingId(id);
        checkInRecord.setCheckInType(CheckInTypeEnum.NORMAL);
        checkInRecord.setCheckInTime(new Date());
        // TODO: 核销食堂信息待补充
        checkInRecord.setCheckInCanteenId(null);
        checkInRecord.setCheckInCanteenCode(null);
        checkInRecord.setCheckInPerson(SysUserHolder.getSysUserInfo().getUserName());
        checkInRecord.setSource(CheckInSourceEnum.MANUAL);
        checkInRecordMapper.insert(checkInRecord);

        bookingMapper.manualWriteOff(id);

        return true;
    }


}
