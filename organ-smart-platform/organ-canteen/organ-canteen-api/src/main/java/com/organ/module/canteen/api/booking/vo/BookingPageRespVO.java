package com.organ.module.canteen.api.booking.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.organ.module.enums.BookingStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class BookingPageRespVO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "用户姓名")
    private String username;

    @ApiModelProperty(value = "所属单位")
    private String belongUnit;

    @ApiModelProperty(value = "食堂名称")
    private String canteenName;

    @ApiModelProperty(value = "餐次")
    private String meal;

    @ApiModelProperty(value = "预约时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date bookingDate;

    @ApiModelProperty(value = "使用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date usageDate;

    @ApiModelProperty(value = "状态")
    private BookingStatusEnum status;
}
