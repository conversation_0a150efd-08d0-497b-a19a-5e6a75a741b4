apiVersion: apps/v1
kind: Deployment
metadata:
    name: organ-gateway #项目名称
    namespace: docker_namespace
spec:
    selector:
        matchLabels:
            app: organ-gateway #项目名称
    template:
        metadata:
            labels:
                app: organ-gateway #项目名称
        spec:
            imagePullSecrets:
              - name: harboreg-pre
            containers:
              - name: organ-gateway #项目名称
                image: image_build_name
                env:
                  - name: CUR_ENV
                    valueFrom:
                        configMapKeyRef:
                            name: credit-organ-config
                            key: profiles.active
                  - name: EUREKA_DEFAULT_ZONE #映射到application中的值
                    valueFrom:
                        configMapKeyRef:
                            name: credit-organ-config #configMap配置
                            key: eureka.defaultzone #配置中的key
                  - name: REDIS_HOST
                    valueFrom:
                        configMapKeyRef:
                            name: credit-organ-config
                            key: redis.host
                  - name: REDIS_PORT
                    valueFrom:
                        configMapKeyRef:
                            name: credit-organ-config
                            key: redis.port
                  - name: REDIS_PASSWORD
                    valueFrom:
                        configMapKeyRef:
                            name: credit-organ-config
                            key: redis.password
                  - name: REDIS_NODES
                    valueFrom:
                        configMapKeyRef:
                            name: credit-organ-config
                            key: redis.nodes
                  - name: SW_AGENT_NAME
                    valueFrom:
                        configMapKeyRef:
                            key: sw.agent.organ.gateway
                            name: skywalking-config
                  - name: SW_AGENT_COLLECTOR_BACKEND_SERVICES
                    valueFrom:
                        configMapKeyRef:
                            key: sw.agent.backend_service
                            name: skywalking-config
                volumeMounts:
                  - mountPath: /home/<USER>/organ-gateway/logs/
                    name: log
                ports:
                  - containerPort: 80
            volumes:
              - name: log
                hostPath:
                    path: /home/<USER>/organ-gateway/logs/
                    type: ''
