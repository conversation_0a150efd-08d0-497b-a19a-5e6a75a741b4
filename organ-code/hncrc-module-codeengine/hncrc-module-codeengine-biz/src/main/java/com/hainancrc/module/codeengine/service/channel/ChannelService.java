package com.hainancrc.module.codeengine.service.channel;

import java.util.*;
import javax.validation.*;
import com.hainancrc.module.codeengine.api.channel.dto.*;
import com.hainancrc.module.codeengine.api.common.IdNameVO;
import com.hainancrc.module.codeengine.entity.ChannelDO;
import com.hainancrc.framework.common.pojo.PageResult;

/**
 * 渠道 Service 接口
 *
 * <AUTHOR>
 */
public interface ChannelService {

    /**
     * 以 key-value pair 方式获取渠道内容
     */
    List<IdNameVO> getKeyValue();

    /**
     * 创建渠道
     *
     * @param createDTO 创建信息
     * @return 编号
     */
    Long createChannel(@Valid ChannelCreateDTO createDTO);

    /**
     * 更新渠道
     *
     * @param updateDTO 更新信息
     */
    void updateChannel(@Valid ChannelUpdateDTO updateDTO);

    /**
     * 删除渠道
     *
     * @param id 编号
     */
    void deleteChannel(Integer id);

    /**
     * 获得渠道
     *
     * @param id 编号
     * @return 渠道
     */
    ChannelDO getChannel(Integer id);

    /**
     * 获得渠道列表
     *
     * @param ids 编号
     * @return 渠道列表
     */
    List<ChannelDO> getChannelList(Collection<Integer> ids);

    /**
     * 获得渠道分页
     *
     * @param pageDTO 分页查询
     * @return 渠道分页
     */
    PageResult<ChannelDO> getChannelPage(ChannelPageDTO pageDTO);

}
