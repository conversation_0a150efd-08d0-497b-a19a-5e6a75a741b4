package com.hainancrc.module.codeengine.configuration;

import java.io.Serializable;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.ToString;

@ConfigurationProperties(prefix = "app")
@ToString(callSuper = true)
@Data
@Component
public class AppConfiguration implements Serializable {
 
    private String qrcodePrefix;

}
