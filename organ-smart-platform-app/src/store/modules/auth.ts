/** 权限管理store类型 */
export interface IAuthStore {
  /** 用户登录token */
  token: string;
  /** 用户信息 */
  userInfo: any;
  /** 用户刷新token */
  refreshToken: string;
  /** 用户权限 */
  permissions: string[];
  /** 用户角色 */
  roles: string[];
  /** 用户 accessToken */
  accessToken: string;
}

/**
 * 权限管理store
 */
export default {
  namespace: "auth",
  state: <IAuthStore>{
    /** 用户登录token */
    token: "",
    /** 用户信息 */
    userInfo: {},
    /** 用户刷新token */
    refreshToken: "",
    /** 用户权限 */
    permissions: [],
    /** 用户角色 */
    roles: [],
    /** 用户 accessToken */
    accessToken: "",
  },
  mutations: {
    setToken(state: IAuthStore, token: string) {
      state.token = token;
    },
    setUserInfo(state, userInfo) {
      state.userInfo = userInfo;
    },
    setRefreshToken(state, refreshToken) {
      state.refreshToken = refreshToken;
    },
    setPermissions(state, permissions) {
      state.permissions = permissions;
    },
    setRoles(state, roles) {
      state.roles = roles;
    },
    setAccessToken(state, accessToken) {
      state.accessToken = accessToken;
    },
  },
};
