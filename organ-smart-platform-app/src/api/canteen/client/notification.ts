import service from "@/service";
import { BIZ_MODULE_API_PREFIX } from "@/api/canteen/constant";
import { INotification } from "./types/notification";

/** 模块API前缀 */
const MODULE_API_PREFIX = `${BIZ_MODULE_API_PREFIX}/notification`;

/** 是否模拟数据 */
const isMock = true;

/** 获取我的未读消息数 */
export function getUnreadNotificationCount() {
  if (isMock) {
    return new Promise<number>((resolve) => {
      resolve(100);
    });
  }
  return service.get<any, number>(`${MODULE_API_PREFIX}/unreadCount`);
}

/** 分页获取我的消息 */
export function getMyNotificationPage(
  params: PaginatedRequestParams & {
    isRead?: boolean;
    notificationType: string;
  }
) {
  if (isMock) {
    console.log("getMyNotificationPage", params);
    return new Promise<PaginatedResponseData<INotification>>((resolve) => {
      const total = 49;
      const start = (params.pageNo - 1) * params.pageSize;
      const list = Array.from({ length: params.pageSize }, (_, index) => ({
        id: `${start + index + 1}`,
        title: "测试消息" + (start + index + 1),
        notificationType: ["ANNOUNCEMENT", "SYSTEM_MSG"][index % 2],
        notificationTypeLabel: ["公告", "系统消息"][index % 2],
        content: "这是一条测试消息".repeat(20),
        createTime: "2024-08-27 10:30:00",
        publishTime: "2024-08-27 10:30:00",
        isRead: (start + index + 1) % 2 === 0,
      }));
      resolve({
        total: total,
        list: list,
      });
    });
  }
  return service.get<any, PaginatedResponseData<INotification>>(
    `${MODULE_API_PREFIX}/myNotifications`,
    {
      params,
    }
  );
}

/** 将消息状态设置为已读 */
export function readNotification(notificationIds: string[]) {
  if (isMock) {
    console.log("将消息状态设置为已读", notificationIds);
    return new Promise<boolean>((resolve) => {
      resolve(true);
    });
  }
  return service.post<any, boolean>(`${MODULE_API_PREFIX}/read`, {
    params: {
      notificationIds,
    },
  });
}

/** 标记为全部已读 */
export function readAllNotification() {
  if (isMock) {
    console.log("标记为全部已读");
    return new Promise<boolean>((resolve) => {
      resolve(true);
    });
  }
  return service.post<any, boolean>(`${MODULE_API_PREFIX}/readAll`);
}
