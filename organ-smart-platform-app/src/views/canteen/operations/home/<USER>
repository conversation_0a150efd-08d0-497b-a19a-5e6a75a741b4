<template>
  <div class="canteen-page canteen-content-pd operations-home-page">
    <!-- 头部 -->
    <div class="header-wrapper row-gap">
      <div class="title">就餐码使用</div>
      <div class="subtitle">扫码就餐作业</div>
    </div>
    <!-- 当前餐次信息 -->
    <CurrentMealCard
      v-if="currentMealPeriod"
      :currentMealPeriod="currentMealPeriod"
      class="row-gap"
    />
    <!-- 扫码提示 -->
    <ScanTipCard class="row-gap" />
    <!-- 当日就餐看板 -->
    <TodayDiningBoard
      v-if="todayDiningBoard"
      :todayDiningBoard="todayDiningBoard"
    />
  </div>
</template>

<script setup lang="ts">
import { getCurrentMealPeriod } from "@/api/canteen/client/mealPeriod";
import { getTodayDiningBoard } from "@/api/canteen/client/stastistic";
import { IMealPeriod } from "@/api/canteen/client/types/mealPeriod";
import { IDailyDiningBoard } from "@/api/canteen/client/types/stastistic";
import { IUser } from "@/api/canteen/client/types/user";
import { getCurrentUserInfo } from "@/api/canteen/client/user";
import { useToast } from "@/components/Toast";
import CurrentMealCard from "./_comp/currentMealCard.vue";
import TodayDiningBoard from "./_comp/todayDiningBoard.vue";
import ScanTipCard from "./_comp/scanTipCard.vue";

/** 当前用户信息 */
const userInfo = ref<IUser>();

/** 正在加载当前用户信息 */
const loadingUserInfo = ref(false);

/** 当日就餐看板 */
const todayDiningBoard = ref<IDailyDiningBoard>();

/** 正在加载当日就餐看板 */
const loadingTodayDiningBoard = ref(false);

/** 当前餐次信息 */
const currentMealPeriod = ref<IMealPeriod>();

/** 正在加载当前餐次信息 */
const loadingCurrentMealPeriod = ref(false);

/** 是否正在加载 */
const loading = computed(() => {
  return (
    loadingUserInfo.value ||
    loadingTodayDiningBoard.value ||
    loadingCurrentMealPeriod.value
  );
});

/** loading 态 */
const toastHook = useToast();
watch(
  () => loading.value,
  (val) => {
    if (val) {
      toastHook.loading("", {
        overlay: true,
        forbidClick: true,
        duration: 0,
      });
    } else {
      toastHook.clear();
    }
  }
);

/** 获取当前登录用户信息 */
const fetchUserInfo = async () => {
  try {
    if (loadingUserInfo.value) return;
    loadingUserInfo.value = true;
    userInfo.value = await getCurrentUserInfo();
  } catch (error) {
    console.error("获取用户信息失败:", error);
  } finally {
    loadingUserInfo.value = false;
  }
};

/** 获取当日就餐看板 */
const fetchTodayDiningBoard = async () => {
  try {
    if (loadingTodayDiningBoard.value) return;
    loadingTodayDiningBoard.value = true;
    todayDiningBoard.value = await getTodayDiningBoard();
  } catch (error) {
    console.error("获取当日就餐看板失败:", error);
  } finally {
    loadingTodayDiningBoard.value = false;
  }
};

/** 获取当前餐次信息 */
const fetchCurrentMealPeriod = async () => {
  try {
    if (loadingCurrentMealPeriod.value) return;
    loadingCurrentMealPeriod.value = true;
    currentMealPeriod.value = await getCurrentMealPeriod();
  } catch (error) {
    console.error("获取当前餐次信息失败:", error);
  } finally {
    loadingCurrentMealPeriod.value = false;
  }
};

/** 初始化页面数据 */
const initPageData = async () => {
  try {
    await Promise.all([
      fetchUserInfo(),
      fetchTodayDiningBoard(),
      fetchCurrentMealPeriod(),
    ]);
  } catch (error) {
    console.error("初始化页面数据失败:", error);
  }
};

onMounted(() => {
  initPageData();
});
</script>

<style scoped lang="scss">
.operations-home-page {
  .header-wrapper {
    padding-top: 50px;

    .title {
      color: #383838;
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      line-height: 1.2;
    }

    .subtitle {
      margin: 0;
      line-height: 1.4;
    }
  }

  .row-gap {
    margin-bottom: 20px;
  }
}
</style>
