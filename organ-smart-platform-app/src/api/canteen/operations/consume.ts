import service from "@/service";
import { BIZ_MODULE_API_PREFIX } from "@/api/canteen/constant";
import { IConsumeResult } from "./types/consume";

/** 模块API前缀 */
const MODULE_API_PREFIX = `${BIZ_MODULE_API_PREFIX}/consume`;

/** 是否模拟数据 */
const isMock = true;

/** 核销就餐码 */
export function consumeDiningCode(consumeCode: string) {
  if (isMock) {
    console.log("consumeDiningCode", consumeCode);
    return new Promise<IConsumeResult>((resolve) => {
      resolve({
        success: Math.random() > 0.5,
        bookingInfo: {
          id: "1",
          canteenName: "学苑食堂",
          mealName: "午餐",
          username: "张三",
          bookingDate: "2024-08-28",
          mealTime: "11:00-13:00",
          bookingCode: "66666",
          status: "CONFIRMED",
          statusLabel: "待使用",
          checkinTime: "2024-08-28 11:00:00",
        },
        errorMessage: "二维码无效或已被使用",
      });
    });
  }
  return service.get<any, IConsumeResult>(`${MODULE_API_PREFIX}/consume`, {
    params: {
      consumeCode,
    },
  });
}
