package com.hainancrc.module.codeengine.service.common;

import java.util.Date;

import com.hainancrc.module.codeengine.entity.ApplyDO;
import com.hainancrc.module.codeengine.entity.CodeRecordDO;
import com.hainancrc.module.codeengine.entity.SceneDO;
import com.hainancrc.module.codeengine.enums.ApplyCategory;

/**
 * 码通用 Service 接口
 *
 * <AUTHOR>
 */
public interface CodeService {

    Boolean applicationRelatedChannel(Long applicationId, Long channelId);

    Boolean applicationRelatedChannels(Long applicationId, Long[] channelIds);

    Boolean applicationRelatedScene(Long applicationId, Long sceneId);

    Boolean applicationRelatedScenes(Long applicationId, Long[] sceneIds);

    Boolean validateExpired(SceneDO sceneDO, ApplyCategory category, Date expiredDate);

    Date sceneExpiredTime(SceneDO sceneDO, ApplyCategory category, Date initDate);

    String comboChannelIdToStr(Long[] channelIds);

    String createApplyOrderId(Long id);    
    
    String createCodeLogOrderId(Long id);  

    CodeRecordDO createCodeRecord(ApplyDO applyDO, String codePrefix,ApplyCategory category);
}