package com.hainancrc.module.codeengine.api.application.dto;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("应用更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApplicationUpdateDTO extends ApplicationCreateDTO {

    @ApiModelProperty(value = "应用ID", required = true)
    @NotNull(message = "应用ID不能为空")
    private Long id;

}
