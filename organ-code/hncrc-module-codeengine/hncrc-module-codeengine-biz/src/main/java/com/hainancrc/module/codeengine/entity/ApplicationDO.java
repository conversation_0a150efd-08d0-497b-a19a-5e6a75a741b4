package com.hainancrc.module.codeengine.entity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 应用 DO
 *
 * <AUTHOR>
 */
@TableName("codeengine_application")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplicationDO extends BaseDO {

    /**
     * 应用ID
     */
    @TableId
    private Long id;
    /**
     * 应用code
     */
    private String applicationCode;
    /**
     * 应用名称
     */
    private String applicationName;
    /**
     * 应用描述
     */
    private String applicationDescription;
    /**
     * 应用重定向地址
     */
    private String applicationCodePrefix;
    /**
     * 是否启用
     */
    private Boolean applicationEnable;

}
