package com.hainancrc.module.codeengine.service.datasource;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.io.IOException;
import java.util.*;

import com.hainancrc.module.codeengine.api.common.IdNameVO;
import com.hainancrc.module.codeengine.api.datasource.dto.*;
import com.hainancrc.module.codeengine.entity.DataSourceDO;
import com.hainancrc.module.codeengine.enums.DataSourceCategory;
import com.hainancrc.module.codeengine.enums.DataSourceHttpMethod;
import com.hainancrc.module.codeengine.enums.DataSourceStatus;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.codeengine.convert.datasource.DataSourceConvert;
import com.hainancrc.module.codeengine.mapper.datasource.DataSourceMapper;

import lombok.var;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;
import okhttp3.Response;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.*;
/**
 * 数据源 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DataSourceServiceImpl implements DataSourceService {

    @Resource
    private DataSourceMapper dataSourceMapper;

    private void validateDataSourceNotExists(String name, String code){
        if(dataSourceMapper.existsByName(name)){
            throw exception(DATA_SOURCE_NAME_EXISTS);
        }

        if(dataSourceMapper.exists(new LambdaQueryWrapperX<DataSourceDO>().eq(DataSourceDO::getDataSourceCode, code))){
            throw exception(DATA_SOURCE_CODE_EXISTS);
        }
    }

    @Override
    public Long createDataSource(DataSourceCreateDTO createDTO) {

        this.validateDataSourceNotExists(createDTO.getDataSourceName(), createDTO.getDataSourceCode());
        
        // 插入
        DataSourceDO dataSource = DataSourceConvert.INSTANCE.convert(createDTO);
        dataSourceMapper.insert(dataSource);
        // 返回
        return dataSource.getId();
    }

    @Override
    public void updateDataSource(DataSourceUpdateDTO updateDTO) {
        // 校验存在
        this.validateDataSourceExists(updateDTO.getId());
        // 更新
        DataSourceDO updateObj = DataSourceConvert.INSTANCE.convert(updateDTO);
        dataSourceMapper.updateById(updateObj);
    }

    @Override
    public void deleteDataSource(Long id) {
        // 校验存在
        this.validateDataSourceExists(id);
        // 删除
        dataSourceMapper.deleteById(id);
    }

    private void validateDataSourceExists(Long id) {
        if (dataSourceMapper.selectById(id) == null) {
            throw exception(DATA_SOURCE_NOT_EXISTS);
        }
    }

    @Override
    public DataSourceDO getDataSource(Long id) {
        return dataSourceMapper.selectById(id);
    }

    @Override
    public List<DataSourceDO> getDataSourceList(Collection<Long> ids) {
        return dataSourceMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<DataSourceDO> getDataSourcePage(DataSourcePageDTO pageDTO) {
        return dataSourceMapper.selectPage(pageDTO);
    }

    @Override
    public List<IdNameVO> getKeyValue() {
        List<DataSourceDO> list = dataSourceMapper.selectList(null);
        List<IdNameVO> result = new ArrayList<>();
        for(var item : list){
            result.add(new IdNameVO(item.getId(), item.getDataSourceName()));
        }
        return result;
    }

    @Override
    public String testDataSource(Long id) {

        try{

            var body = requestDataSource(id);

            var dataSourceDO = dataSourceMapper.selectById(id);
            dataSourceDO.setDataSourceStatus(DataSourceStatus.REACHABLE);
            dataSourceMapper.updateById(dataSourceDO);
            return body;

        } catch (Exception ex) {

            var dataSourceDO = dataSourceMapper.selectById(id);
            dataSourceDO.setDataSourceStatus(DataSourceStatus.UNREACHABLE);
            dataSourceMapper.updateById(dataSourceDO);

            throw ex;

        }

    }

    @Override
    public String requestDataSource(Long id){

        var dataSourceDO = dataSourceMapper.selectById(id);
        if(dataSourceDO == null){
            throw exception(DATA_SOURCE_NOT_EXISTS);
        }
        if(dataSourceDO.getDataSourceCategory() != DataSourceCategory.API){
            throw exception(DATA_SOURCE_NOT_SUPPORT);
        }

        var client = new OkHttpClient().newBuilder().build();

        var builder = new okhttp3.Request.Builder()
                .url(dataSourceDO.getDataSourceUrl());
                
        var headerStr = dataSourceDO.getHeaderParameters();
        
        if(headerStr != null){
            try{
                
                var headers = headerStr.split("\n");
                for(var item : headers){
                    if(item.trim().isEmpty()) continue;

                    var key = item.split(":")[0];
                    var value = item.split(":")[1];
    
                    builder.addHeader(key, value);
                }
                
            }catch(Exception e){
                throw exception(DATA_SOURCE_HEADER_PARSE_ERROR);
            }
        }

        if(dataSourceDO.getQueryParameters() != null && dataSourceDO.getDataSourceMethod() !=  DataSourceHttpMethod.GET){
            //默认是 JSON
            builder.addHeader("Content-Type", "application/json");
            var bodyBuff = dataSourceDO.getQueryParameters().getBytes();
            var body = RequestBody.create(bodyBuff);
            builder.method(dataSourceDO.getDataSourceMethod().toString(), body);
        }

        builder.addHeader("User-Agent", "hncrc-codeengine-center/1.0.0");

        var request = builder.build();

        Response response = null;
        try {
            response = client.newCall(request).execute();
        } catch (IOException e) {
            throw exception(DATA_SOURCE_REQUEST_ERROR);
        }

        if(response.code() < 200 || response.code() >= 210){    //20x 的都算成功
            throw exception(DATA_SOURCE_REQUEST_STATUS_ERROR);
        }

        //将 response 的 body 转换成字符串
        var body = response.body();
        String bodyStr = null;
        try {
            bodyStr = body.string();
        } catch (IOException e) {
            throw exception(DATA_SOURCE_REQUEST_BODY_ERROR);
        }

        return bodyStr;

    }

}
