package com.hainancrc.module.codeengine.api.publicapi.vo;

import com.hainancrc.module.codeengine.enums.CodeLogStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("GenerateAuthRespVO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GenerateAuthRespVO {

    @ApiModelProperty(value = "亮码状态(authRequired/fail/success)", required = true)
    private CodeLogStatus generateStatus;
    
}