package com.hainancrc.module.codeengine.controller.publish;

import java.io.IOException;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hainancrc.module.codeengine.service.publish.PublishService;

import io.swagger.annotations.ApiOperation;
import lombok.var;
import springfox.documentation.annotations.ApiIgnore;

@ApiIgnore
@RestController
@RequestMapping("/codeengine/publish")
@Validated
public class PublishController {

    @Resource
    private PublishService publishService;
    
    // 请参考 CodeServiceImpl 的 createCodeRecord 方法
    @GetMapping("/")
    @ApiOperation("解码状态查询")
    public void publishEntry(@RequestParam("q") String q, @RequestParam("e") String e, HttpServletResponse httpServletResponse) throws IOException {

        var redirectUrl = publishService.publishEntry(q, e);
        httpServletResponse.sendRedirect(redirectUrl);
        
    }

}
