package com.hainancrc.module.codeengine.mapper.codelog;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.framework.mybatis.core.mapper.BaseMapperX;
import com.hainancrc.module.codeengine.api.codelog.dto.CodeLogPageDTO;
import com.hainancrc.module.codeengine.api.codelog.vo.CodeLogRespVO;
import com.hainancrc.module.codeengine.entity.CodeLogDO;


import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 生解码操作记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CodeLogMapper extends BaseMapperX<CodeLogDO> {

	default CodeLogDO selectByOrderId(String orderId){
        return selectOne(new LambdaQueryWrapperX<CodeLogDO>().eq(CodeLogDO::getCodeLogOrderId, orderId));
    }

    IPage<CodeLogRespVO> selectCodeLogPage(IPage<CodeLogRespVO> page, @Param("dto") CodeLogPageDTO dto);

    CodeLogRespVO selectCodeLogOne(@Param("id") Long id);


}
