package  com.hainancrc.module.codeengine.api.application.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;


@ApiModel(value = "应用 Excel 导出 Request VO", description = "参数和 ApplicationPageReqVO 是一致的")
@Data
public class ApplicationExportListDTO {

    @ApiModelProperty(value = "应用code")
    private String applicationCode;
    @ApiModelProperty(value = "应用名称")
    private String applicationName;
    @ApiModelProperty(value = "应用描述")
    private String applicationDescription;
    @ApiModelProperty(value = "是否启用")
    private Boolean applicationEnable;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
