package com.hainancrc.module.codeengine.mapper.application;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.hainancrc.module.codeengine.entity.ApplicationDO;
import com.hainancrc.module.codeengine.mapper.CodeBaseMapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.hainancrc.module.codeengine.api.application.dto.*;
import com.hainancrc.module.codeengine.api.application.vo.*;

/**
 * 应用 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ApplicationMapper extends CodeBaseMapper<ApplicationDO> {

    default Boolean existsByName(String name) {
        return exists(new LambdaQueryWrapperX<ApplicationDO>().eq(ApplicationDO::getApplicationName, name));
    }

    IPage<ApplicationRespVO> selectApplictionPage(IPage<ApplicationRespVO> page, @Param("dto") ApplicationPageDTO reqDTO);

    default PageResult<ApplicationDO> selectPage(ApplicationPageDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<ApplicationDO>()
                .likeIfPresent(ApplicationDO::getApplicationCode, reqDTO.getApplicationCode())
                .likeIfPresent(ApplicationDO::getApplicationName, reqDTO.getApplicationName())
                .eqIfPresent(ApplicationDO::getApplicationEnable, reqDTO.getApplicationEnable())
                .betweenIfPresent(ApplicationDO::getCreateTime, reqDTO.getCreateTimeStart(), reqDTO.getCreateTimeEnd())
                .orderByDesc(ApplicationDO::getId));
    }

    default ApplicationDO selectByCode(String code){
        return selectOne(new LambdaQueryWrapperX<ApplicationDO>().eq(ApplicationDO::getApplicationCode, code));
    }

}
