package com.hainancrc.module.codeengine.api.applicationchannelrel.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import com.hainancrc.module.codeengine.api.applicationchannelrel.dto.*;

@ApiModel("应用渠道多对多 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApplicationChannelRelRespVO extends ApplicationChannelRelBaseDTO {

    @ApiModelProperty(value = "主键ID", required = true)
    private Integer id;

    @ApiModelProperty(value = "创建时间", required = true)
    private Date createTime;

}
