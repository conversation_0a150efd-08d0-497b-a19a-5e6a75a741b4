package com.hainancrc.module.codeengine.service.publicapi;

import java.util.List;

import javax.validation.Valid;

import com.hainancrc.module.codeengine.api.common.KeyNameVO;
import com.hainancrc.module.codeengine.api.publicapi.dto.*;
import com.hainancrc.module.codeengine.api.publicapi.vo.*;
import com.hainancrc.module.codeengine.enums.ApplyCategory;
import com.hainancrc.module.codeengine.enums.CodeLogCategory;

/**
 * 此接口定义了代码引擎模块的 API 服务。
 * 它提供了创建、检索和复核申领、生成和解码操作的方法。
 */
public interface PublicApiService {
    
    /**
     * 使用提供的数据创建一个申领操作。
     * @param createDTO 申领操作的数据。
     * @return 创建的申领操作的响应数据。
     */
    ApplyCreateRespVO createApply(@Valid ApplyCreateDTO createDTO);
    
    /**
     * 使用提供的数据和身份验证复核一个申领操作。
     * @param authDTO 申领操作的数据和身份验证。
     * @return 复核的申领操作的响应数据。
     */
    ApplyStatusRespVO applyWithAuth(@Valid ApplyWithAuthDTO authDTO);
    
    /**
     * 检索具有提供的订单 ID 的申领操作的状态。
     * @param statusDTO
     * @return
     */
    ApplyStatusRespVO applyStatus(@Valid ApplyStatusDTO statusDTO);

    /**
     * 使用提供的数据创建一个生成操作。
     * @param createDTO 生成操作的数据。
     * @return 创建的生成操作的响应数据。
     */
    GenerateCreateRespVO createGenerate(@Valid GenerateCreateDTO createDTO);
    
    /**
     * 使用提供的数据和身份验证复核一个生成操作。
     * @param authDTO 生成操作的数据和身份验证。
     * @return 复核的生成操作的响应数据。
     */
    GenerateAuthRespVO generateWithAuth(@Valid GenerateWithAuthDTO authDTO);
    
    /**
     * 检索具有提供的订单 ID 的生成操作的状态。
     * @param orderId 生成操作的订单 ID。
     * @return 生成操作状态的响应数据。
     */
    GenerateStatusRespVO generateStatus(@Valid GenerateStatusDTO statusDTO);

    /**
     * 使用提供的数据创建一个解码操作。
     * @param createDTO 解码操作的数据。
     * @return 创建的解码操作的响应数据。
     */
    DecodeCreateRespVO createDecode(@Valid DecodeCreateDTO createDTO);
    
    /**
     * 使用提供的数据和身份验证复核一个解码操作。
     * @param authDTO 解码操作的数据和身份验证。
     * @return 复核的解码操作的响应数据。
     */
    DecodeAuthRespVO decodeWithAuth(@Valid DecodeWithAuthDTO authDTO);
    
    /**
     * 检索具有提供的订单 ID 的解码操作的状态。
     * @param orderId 解码操作的订单 ID。
     * @return 解码操作状态的响应数据。
     */
    DecodeStatusRespVO decodeStatus(@Valid DecodeStatusDTO statusDTO);

    /**
     * 检索场景列表。
     * @return 场景列表。
     */
    List<KeyNameVO> sceneList(String application);

    /**
     * 检索场景对应动作ID。
     * @return 场景动作ID。
     */
    String getSceneAuthId(String application,
                          String scene,
                          CodeLogCategory category,
                          ApplyCategory applyCategory);

}
