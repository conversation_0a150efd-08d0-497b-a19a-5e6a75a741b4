package com.hainancrc.module.codeengine.api.publicapi.vo;

import com.hainancrc.module.codeengine.enums.CodeLogStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("GenerateCreateRespVO")
@Data
public class GenerateCreateRespVO {

    @ApiModelProperty(value = "亮码订单ID", required = true)
    private String generateOrderId;

    @ApiModelProperty(value = "亮码状态(authRequired/fail/success)", required = true)
    private CodeLogStatus generateStatus;

    @ApiModelProperty(value = "亮码授权动作ID", required = false)
    private String authActionId;

}