package com.hainancrc.module.codeengine.service.codelog;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hainancrc.module.codeengine.api.codelog.dto.CodeLogPageDTO;
import com.hainancrc.module.codeengine.api.codelog.dto.CodeLogQueryDTO;
import com.hainancrc.module.codeengine.api.codelog.vo.CodeLogRespVO;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.module.codeengine.api.codelog.vo.SimpleCodeLogRespVO;

/**
 * 应用 Service 接口
 *
 * <AUTHOR>
 */
public interface CodeLogService {
    /**
     * 获得码记录分页
     *
     * @param pageDTO 分页查询
     * @return 应用分页
     */

    PageResult<CodeLogRespVO> getCodeLogPage(CodeLogPageDTO pageDTO);

    /**
     * 获得码记录
     *
     * @param id 编号
     * @return
     */
    CodeLogRespVO getCodeLog(Long id);


    PageResult<SimpleCodeLogRespVO> querySimpleLogPage(CodeLogQueryDTO queryDTO);

}
