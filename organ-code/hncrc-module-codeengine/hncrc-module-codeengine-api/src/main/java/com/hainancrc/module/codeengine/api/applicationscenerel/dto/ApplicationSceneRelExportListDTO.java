package  com.hainancrc.module.codeengine.api.applicationscenerel.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;


@ApiModel(value = "应用场景多对多 Excel 导出 Request VO", description = "参数和 ApplicationSceneRelPageReqVO 是一致的")
@Data
public class ApplicationSceneRelExportListDTO {

    @ApiModelProperty(value = "应用ID")
    private Integer applicationId;
    @ApiModelProperty(value = "场景ID")
    private Integer sceneId;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
