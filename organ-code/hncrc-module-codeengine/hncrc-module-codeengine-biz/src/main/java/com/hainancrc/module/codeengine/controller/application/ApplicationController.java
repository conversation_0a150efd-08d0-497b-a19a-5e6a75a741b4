package com.hainancrc.module.codeengine.controller.application;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.annotations.*;
import javax.validation.*;
import java.util.*;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.common.pojo.CommonResult;
import static com.hainancrc.framework.common.pojo.CommonResult.success;


import com.hainancrc.module.codeengine.api.application.dto.*;
import com.hainancrc.module.codeengine.api.application.vo.*;
import com.hainancrc.module.codeengine.api.common.IdNameVO;
import com.hainancrc.module.codeengine.entity.ApplicationDO;
import com.hainancrc.module.codeengine.convert.application.ApplicationConvert;
import com.hainancrc.module.codeengine.service.application.ApplicationService;

@Api(tags = "应用")
@RestController
@RequestMapping("/codeengine/application")
@Validated
public class ApplicationController {

    @Resource
    private ApplicationService applicationService;

    @GetMapping("/keyValue")
    @ApiOperation("以 key-value 方式获码应用内容")
    @ApiImplicitParam(name = "applicationEnable",
            value = "启用状态,不传默认全部", required = false, dataTypeClass = Boolean.class)
    public CommonResult<List<IdNameVO>> getKeyValue(@RequestParam(name="applicationEnable",required = false)
                                                                Boolean applicationEnable) {

        return success(applicationService.getKeyValue(applicationEnable));
    }


    @PostMapping("/create")
    @ApiOperation("创建应用")
    public CommonResult<Long> createApplication(@Valid @RequestBody ApplicationCreateDTO createDTO) {
        return success(applicationService.createApplication(createDTO));
    }

    @PutMapping("/update")
    @ApiOperation("更新应用")
    public CommonResult<Boolean> updateApplication(@Valid @RequestBody ApplicationUpdateDTO updateDTO) {
        applicationService.updateApplication(updateDTO);
        return success(true);
    }

    @PutMapping("/enable")
    @ApiOperation("启用应用")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> enableApplication(@RequestParam("id") Long id) {
        applicationService.enableApplication(id);
        return success(true);
    }

    @PutMapping("/disable")
    @ApiOperation("禁用应用")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    public CommonResult<Boolean> disableApplication(@RequestParam("id") Long id) {
        applicationService.disableApplication(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得应用")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<ApplicationRespVO> getApplication(@RequestParam("id") Long id) {
        return success(applicationService.getApplication(id));
    }

    @GetMapping("/list")
    @ApiOperation("获得应用列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    public CommonResult<List<ApplicationRespVO>> getApplicationList(@RequestParam("ids") Collection<Long> ids) {
        List<ApplicationDO> list = applicationService.getApplicationList(ids);
        return success(ApplicationConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得应用分页")
    public CommonResult<PageResult<ApplicationRespVO>> getApplicationPage(@Valid ApplicationPageDTO pageDTO) {
        PageResult<ApplicationRespVO> pageResult = applicationService.getApplicationPage(pageDTO);
        return success(pageResult);
    }

    @PostMapping("/relateScene")
    @ApiOperation("关联场景")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class),
        @ApiImplicitParam(name = "sceneIds", value = "场景编号(格式 1,2,3)", required = true, dataTypeClass = String.class)
    })
    public CommonResult<Boolean> relateApplication(@RequestParam("id") Long id, @RequestParam("sceneIds") String sceneIds) {
        applicationService.relateScene(id, sceneIds);
        return success(true);
    }

    @GetMapping("/getSceneKeyValue")
    @ApiOperation("以 key-value 方式获取渠道内容")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    public CommonResult<List<IdNameVO>> getSceneKeyValue(@RequestParam("id") Long id) {
        return success(applicationService.getSceneKeyValue(id));
    }

    @PostMapping("/relateChannel")
    @ApiOperation("关联渠道")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class),
        @ApiImplicitParam(name = "channelIds", value = "渠道编号(格式 1,2,3)", required = true, dataTypeClass = String.class)
    })
    public CommonResult<Boolean> relateChannel(@RequestParam("id") Long id, @RequestParam("channelIds") String channelIds) {
        applicationService.relateChannel(id, channelIds);
        return success(true);
    }

}
