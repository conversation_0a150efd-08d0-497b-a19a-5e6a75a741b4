<template>
  <div class="current-meal-card">
    <div class="label">食堂名称</div>
    <div class="value">{{ currentMealPeriod.canteenName }}</div>
    <div class="label">餐次名称</div>
    <div class="value mb-0">{{ currentMealPeriod.mealName }}</div>
    <div class="label mb-0">
      {{ fmtDiningDtime }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { IMealPeriod } from "@/api/canteen/client/types/mealPeriod";
import dayjs from "dayjs";

const props = defineProps<{
  currentMealPeriod: IMealPeriod;
}>();

/** 格式化就餐时间 */
const fmtDiningDtime = computed(() => {
  if (!props.currentMealPeriod) return "";
  const { startTime, endTime } = props.currentMealPeriod;
  return `${dayjs().format("YYYY年MM月DD日")} ${startTime} - ${endTime}`;
});
</script>

<style scoped lang="scss">
.current-meal-card {
  background: #409eff;
  border-radius: 6px;
  padding: 20px 12px;
  color: #ffffff;

  .label {
    font-size: 14px;
    margin-bottom: 6px;
  }

  .value {
    font-size: 20px;
    margin-bottom: 16px;
  }

  .mb-0 {
    margin-bottom: 0;
  }
}
</style>
