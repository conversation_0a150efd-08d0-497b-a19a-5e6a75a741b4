<template>
  <div
    v-if="visible"
    class="notification-detail-overlay"
    @click.stop="handleOverlayClick"
  >
    <div class="notification-detail-dialog" @click.stop>
      <!-- 通知详情 -->
      <div class="notification-detail-title pd-20">
        <div class="title-wrapper">
          <div class="title-text">
            {{ notificationDetail.title }}
          </div>
          <div
            class="canteen-tag notification-type"
            :style="getNotificationTypeStyle"
          >
            {{ notificationDetail.notificationTypeLabel }}
          </div>
        </div>
        <!-- 发布日期 -->
        <div v-if="notificationDetail.publishTime" class="publish-time">
          {{ dayjs(notificationDetail.publishTime).format("YYYY年M月D日") }}
        </div>
      </div>
      <div class="notification-detail-content">
        {{ notificationDetail.content }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { INotification } from "@/api/canteen/client/types/notification";
import { NotificationTypeEnum } from "@/views/canteen/configs/enums/NotificationTypeEnum";
import dayjs from "dayjs";

/**
 * 用户信息弹窗组件Props接口
 */
interface IProps {
  /** 是否显示弹窗 */
  value: boolean;
  /** 预约信息 */
  notificationDetail: INotification;
  /** 是否显示footer */
  showFooter?: boolean;
}

/** 消息通知状态的样式配置 */
interface IStatusStyle {
  backgroundColor: string;
  color: string;
}

/**
 * 组件事件接口
 */
interface IEmits {
  (e: "input", visible: boolean): void;
}

const props = withDefaults(defineProps<IProps>(), {
  value: false,
});
const emit = defineEmits<IEmits>();

/** 是否显示弹窗 */
const visible = computed({
  get() {
    return props.value;
  },
  set(value) {
    emit("input", value);
  },
});

/** 默认的消息通知状态样式配置 */
const notificationTypeStyleConfig: Record<
  NotificationTypeEnum | "DEFAULT",
  IStatusStyle
> = {
  [NotificationTypeEnum.ANNOUNCEMENT]: {
    backgroundColor: "#F0F9EB",
    color: "#2DCD6B",
  },
  [NotificationTypeEnum.SYSTEM_MSG]: {
    backgroundColor: "var(--color-primary-plain-bg)",
    color: "var(--color-primary)",
  },
  DEFAULT: {
    backgroundColor: "var(--color-primary-plain-bg)",
    color: "var(--color-primary)",
  },
};

/** 获取消息通知状态的样式配置 */
const getNotificationTypeStyle = computed(() => {
  const notificationType = props.notificationDetail.notificationType;
  if (notificationType in notificationTypeStyleConfig) {
    return notificationTypeStyleConfig[notificationType];
  }
  return notificationTypeStyleConfig.DEFAULT;
});

/**
 * 处理遮罩层点击事件
 */
const handleOverlayClick = () => {
  emit("input", false);
};

/** 关闭弹窗 */
const handleClose = () => {
  emit("input", false);
};
</script>

<style scoped lang="scss">
.notification-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;

  .notification-detail-dialog {
    background: #ffffff;
    border-radius: 10px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;

    .notification-detail-content {
      padding: 20px;
      color: #383838;
      font-weight: 400;
    }

    .notification-detail-title {
      padding: 20px 20px 10px;
      border-bottom: 1px solid #eee;

      .title-wrapper {
        display: flex;
        align-items: center;
        column-gap: 10px;
        margin-bottom: 8px;

        .title-text {
          font-size: 18px;
          color: #000000;
          line-height: 1;
          margin: 0;
        }
      }
      .publish-time {
        font-size: 14px;
        color: #a6a6a6;
        line-height: 1;
        margin: 0;
      }
    }
  }
}
</style>
