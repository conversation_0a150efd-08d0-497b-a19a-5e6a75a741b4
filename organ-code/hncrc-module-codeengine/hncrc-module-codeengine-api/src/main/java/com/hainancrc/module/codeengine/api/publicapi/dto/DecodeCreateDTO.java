package com.hainancrc.module.codeengine.api.publicapi.dto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("解码记录创建 Request VO")
@Data
public class DecodeCreateDTO {

    @ApiModelProperty(value = "应用Code", required = true)
    @NotNull(message = "应用Code不能为空")
    private String application;

    @ApiModelProperty(value = "渠道Code", required = true)
    @NotNull(message = "渠道Code不能为空")
    private String channel;

    @ApiModelProperty(value = "解码内容", required = true)
    @NotEmpty(message = "解码内容不能为空")
    private String codeUrl;

    @ApiModelProperty(value = "请求解码主体", required = false)
    private String requestEntity;

    @ApiModelProperty(value = "请求解码主体Id", required = false)
    private String requestEntityId;

}