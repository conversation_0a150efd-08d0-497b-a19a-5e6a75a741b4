package  com.hainancrc.module.codeengine.api.coderecord;

import lombok.*;
import io.swagger.annotations.*;

import java.util.Date;

import javax.validation.constraints.*;

import com.fasterxml.jackson.annotation.JsonFormat;

@ApiModel("生成码 Request VO")
@Data
public class CodeRecordCreateDTO {

    @ApiModelProperty(value = "码应用id",required = true)
    @NotNull(message = "码应用id不能为空")
    private Long applicationId;

    @ApiModelProperty(value = "场景id",required = true)
    @NotNull(message = "场景id不能为空")
    private Long sceneId;

    @ApiModelProperty(value = "主体id",required = true)
    private String entityId;

    @ApiModelProperty(value = "主体",required = true)
    private String entity;

    @ApiModelProperty(value = "渠道ids",required = true)
    @NotNull(message = "渠道ids不能为空")
    private Long[] channelIds;

    @ApiModelProperty(value="码有效期",required = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expiredTime;

    @ApiModelProperty(value = "申码描述",required = true)
    private String applyDescription;
}