package com.hainancrc.module.codeengine.entity;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 批量生成 DO
 *
 * <AUTHOR>
 */
@TableName("codeengine_batch_generate")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchGenerateDO extends BaseDO {

    /**
     * 批量生成ID
     */
    @TableId
    private Long id;
    /**
     * 申领id
     */
    private Long applicationId;
    /**
     * 生成订单号
     */
    private String generateOrderId;
    /**
     * 生成数量
     */
    private Integer generateCount;

}
