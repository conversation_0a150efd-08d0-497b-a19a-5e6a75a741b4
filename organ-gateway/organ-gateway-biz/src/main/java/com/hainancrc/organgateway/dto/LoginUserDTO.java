package com.hainancrc.organgateway.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LoginUserDTO {

    @ApiModelProperty(value = "用户id")
    private Long id;

    @ApiModelProperty(value = "用户账号")
    private String userAccount;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "1系统用户，2金融机构用户,3基础平台用户")
    private Integer tenantId;

}
