package com.hainancrc.module.codeengine.api.application.vo;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;
import com.hainancrc.module.codeengine.api.application.dto.*;

@ApiModel("应用 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApplicationRespVO extends ApplicationBaseDTO {

    @ApiModelProperty(value = "应用ID", required = true)
    private Long id;

    @ApiModelProperty(value = "创建时间", required = true)
    private Date createTime;

    @ApiModelProperty(value = "数据源Id", required = false)
    private Long[] dataSourceId;

    @ApiModelProperty(value = "场景Id", required = false)
    private Long[] sceneId;

    @ApiModelProperty(value = "渠道Id", required = false)
    private Long[] channelId;

}
