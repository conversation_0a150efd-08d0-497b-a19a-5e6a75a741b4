package com.hainancrc.module.codeengine.api.application.dto;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

/**
* 应用
*/
@Data
public class ApplicationBaseDTO {

    @ApiModelProperty(value = "应用code", required = true)
    @NotNull(message = "应用code不能为空")
    private String applicationCode;

    @ApiModelProperty(value = "应用名称", required = true)
    @NotNull(message = "应用名称不能为空")
    @Pattern(regexp = "^[^\\s]*$", message = "应用名称不能包含空格")
    private String applicationName;

    @ApiModelProperty(value = "应用码串前缀")
    private String applicationCodePrefix;

    @ApiModelProperty(value = "应用描述")
    private String applicationDescription;

    @ApiModelProperty(value = "是否启用", required = true)
    @NotNull(message = "是否启用不能为空")
    private Boolean applicationEnable;

}
