package com.hainancrc.module.codeengine.api.coderecord;

import java.util.Date;

import com.hainancrc.module.codeengine.enums.ApplyCategory;
import lombok.Data;

@Data
public class CodeRecordBatchDTO{

    //场景 id
    private Long sceneId;
    // 场景码logo
    private String sceneCodeLogo;

    //主体id
    private String entityId;

    //主体不能为空
    private String entity;

    //渠道id
    private Long[] channelIds;

    //码有效期
    private Date expiredTime;

    //申码描述
    private String applyDescription;

    private ApplyCategory codeCategory;
}