package com.hainancrc.module.codeengine.api.codelog.dto;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

import com.hainancrc.module.codeengine.enums.CodeLogCategory;
import com.hainancrc.module.codeengine.enums.CodeLogStatus;

/**
* 生解码操作记录
*/
@Data
public class CodeLogBaseDTO {

    @ApiModelProperty(value = "操作订单号", required = true)
    @NotNull(message = "操作订单号不能为空")
    private String codeLogOrderId;

    @ApiModelProperty(value = "操作类型(apply/generate/decode)", required = true)
    @NotNull(message = "操作类型(apply/generate/decode)不能为空")
    private CodeLogCategory codeLogCategory;

    @ApiModelProperty(value = "授权场景ID", required = true)
    @NotNull(message = "授权场景ID不能为空")
    private String codeLogAuthId;

    @ApiModelProperty(value = "授权场景订单号")
    private String codeLogAuthOrderId;

    @ApiModelProperty(value = "申码记录ID")
    private Long codeLogApplyId;

    @ApiModelProperty(value = "二维码key")
    private String codeKey;

    @ApiModelProperty(value = "操作状态(authRequired/fail/success)", required = true)
    @NotNull(message = "操作状态(authRequired/fail/success)不能为空")
    private CodeLogStatus logStatus;

    @ApiModelProperty(value = "主体ID")
    private String entityId;

    @ApiModelProperty(value = "主体")
    private String entity;

    @ApiModelProperty(value = "请求主体ID(解码时)")
    private String requestEntityId;

    @ApiModelProperty(value = "请求主体(解码时)")
    private String requestEntity;

    @ApiModelProperty(value = "场景名称")
    private String sceneName;

    private Long sceneId;

    private String channelIdStr;

    @ApiModelProperty(value = "码应用名称")
    private String applicationName;
    private Long applicationId;
    @ApiModelProperty(value = "用户名")
    private String createName;
}
