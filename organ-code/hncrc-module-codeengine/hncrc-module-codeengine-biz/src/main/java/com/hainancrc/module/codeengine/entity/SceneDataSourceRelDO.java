package com.hainancrc.module.codeengine.entity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;

/**
 * 场景数据源 DO
 *
 * <AUTHOR>
 */
@TableName("codeengine_scene_data_source_rel")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SceneDataSourceRelDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 场景ID
     */
    private Long sceneId;
    /**
     * 数据源ID
     */
    private Long dataSourceId;

}
