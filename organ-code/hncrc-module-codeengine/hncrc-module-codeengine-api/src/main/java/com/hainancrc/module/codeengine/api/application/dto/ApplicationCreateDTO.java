package  com.hainancrc.module.codeengine.api.application.dto;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("应用创建 Request VO")
@Data
public class ApplicationCreateDTO {

    @ApiModelProperty(value = "应用名称", required = true)
    @NotEmpty(message = "应用名称不能为空")
    @Pattern(regexp = "^[^\\s]*$", message = "应用名称不能包含空格")
    private String applicationName;

    @ApiModelProperty(value = "场景ID", required = true)
    @NotNull(message = "场景ID不能为空")
    private Long[] sceneId;

    @ApiModelProperty(value = "渠道ID", required = true)
    @NotNull(message = "渠道ID不能为空")
    private Long[] channelId;

    @ApiModelProperty(value = "应用描述")
    private String applicationDescription;

    @ApiModelProperty(value = "应用码串前缀")
    private String applicationCodePrefix;

}
