package com.hainancrc.module.codeengine.service.channel;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.hainancrc.framework.redis.service.RedisService;
import com.hainancrc.framework.security.SystemLoginUser;
import com.hainancrc.framework.security.core.util.TokenUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.hainancrc.module.codeengine.api.channel.dto.*;
import com.hainancrc.module.codeengine.api.common.IdNameVO;
import com.hainancrc.module.codeengine.entity.ChannelDO;
import com.hainancrc.framework.common.pojo.PageResult;

import com.hainancrc.module.codeengine.convert.channel.ChannelConvert;
import com.hainancrc.module.codeengine.mapper.channel.ChannelMapper;

import static com.hainancrc.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.hainancrc.module.codeengine.api.enums.ErrorCodeConstants.*;
/**
 * 渠道 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ChannelServiceImpl implements ChannelService {

    @Resource
    private ChannelMapper channelMapper;

    @Resource
    private RedisService redisService;

    private static final String CHANNEL_CACHE_KEY = "code:engine:channel:%s";

    private void validateChannelNotExists(String name){
        if(channelMapper.existsByName(name)){
            throw exception(CHANNEL_NAME_EXISTS);
        }
    }


    @Override
    public Long createChannel(ChannelCreateDTO createDTO) {

        this.validateChannelNotExists(createDTO.getChannelName());

        // 插入
        ChannelDO channel = ChannelConvert.INSTANCE.convert(createDTO);

        SystemLoginUser systemLoginUser= TokenUtils.getSysLoginUser(redisService);
        if(null!=systemLoginUser){
            log.info("当前登录用户为:{}",systemLoginUser);
            channel.setPresenter(systemLoginUser.getUserAccount());
        }

        channelMapper.insert(channel);
        redisService.set(String.format(CHANNEL_CACHE_KEY,channel.getChannelCode()), JSON.toJSONString(channel));
        // 返回
        return channel.getId();
    }

    @Override
    public void updateChannel(ChannelUpdateDTO updateDTO) {
        // 校验存在
        this.validateChannelExists(updateDTO.getId());
        // 更新
        ChannelDO updateObj = ChannelConvert.INSTANCE.convert(updateDTO);
        SystemLoginUser systemLoginUser= TokenUtils.getSysLoginUser(redisService);
        if(null!=systemLoginUser){
            log.info("当前登录用户为:{}",systemLoginUser);
            updateObj.setPresenter(systemLoginUser.getUserAccount());
        }
        channelMapper.updateById(updateObj);
        redisService.set(String.format(CHANNEL_CACHE_KEY,updateObj.getChannelCode()), JSON.toJSONString(updateObj));
    }

    @Override
    public void deleteChannel(Integer id) {
        // 校验存在
        ChannelDO channelDO = channelMapper.selectById(id);
        if (channelDO == null) {
            throw exception(CHANNEL_NOT_EXISTS);
        }
        // 删除
        channelMapper.deleteById(id);
        redisService.deleteObject(String.format(CHANNEL_CACHE_KEY,channelDO.getChannelCode()));
    }

    private void validateChannelExists(Integer id) {
        if (channelMapper.selectById(id) == null) {
            throw exception(CHANNEL_NOT_EXISTS);
        }
    }

    @Override
    public ChannelDO getChannel(Integer id) {
        return channelMapper.selectById(id);
    }

    @Override
    public List<ChannelDO> getChannelList(Collection<Integer> ids) {
        return channelMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<ChannelDO> getChannelPage(ChannelPageDTO pageDTO) {
        return channelMapper.selectPage(pageDTO);
    }

    @Override
    public List<IdNameVO> getKeyValue() {
        List<ChannelDO> list = channelMapper.selectList(null);
        List<IdNameVO> result = new ArrayList<>();
        for (ChannelDO channel : list) {
            result.add(new IdNameVO(channel.getId(), channel.getChannelName()));
        }
        return result;
    }



}
