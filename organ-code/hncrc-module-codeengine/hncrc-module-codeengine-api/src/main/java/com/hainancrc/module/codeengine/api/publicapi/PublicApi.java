package com.hainancrc.module.codeengine.api.publicapi;

import java.util.List;

import com.hainancrc.module.codeengine.api.coderecord.CodeRecordCreateDTO;
import com.hainancrc.module.codeengine.api.coderecord.RequireCodeInfoCreateDTO;
import com.hainancrc.module.codeengine.api.coderecord.vo.CodeRecordGeneratorCodeInfoRespVO;
import com.hainancrc.module.codeengine.api.coderecord.vo.CodeRecordGeneratorCodeRespVO;
import com.hainancrc.module.codeengine.enums.ApplyCategory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.hainancrc.framework.common.pojo.CommonResult;
import com.hainancrc.module.codeengine.api.common.KeyNameVO;
import com.hainancrc.module.codeengine.api.enums.ApiConstants;
import com.hainancrc.module.codeengine.api.publicapi.dto.*;
import com.hainancrc.module.codeengine.api.publicapi.vo.*;
import com.hainancrc.module.codeengine.enums.CodeLogCategory;

import io.swagger.annotations.ApiOperation;

import javax.validation.Valid;

@FeignClient(name = ApiConstants.NAME)
public interface PublicApi {
    String PREFIX = "/codeengine/publicapi";

    @GetMapping(PREFIX + "/sceneList")
    CommonResult<List<KeyNameVO>> sceneList(@RequestParam("application")String application);

    @GetMapping(PREFIX + "/sceneAuthId")
    @ApiOperation("检索场景对应动作ID。")
    CommonResult<String> getSceneAuthId(
            @RequestParam("application") String application,
            @RequestParam("scene") String scene,
            @RequestParam("category") CodeLogCategory category,
            @RequestParam("applyCategory") ApplyCategory applyCategory);

    @PostMapping(PREFIX + "/apply")
    CommonResult<ApplyCreateRespVO> createApply(@RequestBody ApplyCreateDTO createDTO);

    @PostMapping(PREFIX + "/applyWithAuth")
    CommonResult<ApplyStatusRespVO> applyWithAuth(@RequestBody ApplyWithAuthDTO authDTO);

    @PostMapping(PREFIX + "/applyStatus")
    CommonResult<ApplyStatusRespVO> applyStatus(@RequestBody ApplyStatusDTO statusDTO);

    @PostMapping(PREFIX + "/generate")
    CommonResult<GenerateCreateRespVO> createGenerate(@RequestBody GenerateCreateDTO createDTO);

    @PostMapping(PREFIX + "/generateWithAuth")
    CommonResult<GenerateAuthRespVO> generateWithAuth(@RequestBody GenerateWithAuthDTO authDTO);

    @PostMapping(PREFIX + "/generateStatus")
    CommonResult<GenerateStatusRespVO> generateStatus(@RequestBody GenerateStatusDTO statusDTO);

    @PostMapping(PREFIX + "/decode")
    CommonResult<DecodeCreateRespVO> createDecode(@RequestBody DecodeCreateDTO createDTO);

    @PostMapping(PREFIX + "/decodeWithAuth")
    CommonResult<DecodeAuthRespVO> decodeWithAuth(@RequestBody DecodeWithAuthDTO authDTO);

    @PostMapping(PREFIX + "/decodeStatus")
    CommonResult<DecodeStatusRespVO> decodeStatus(@RequestBody DecodeStatusDTO statusDTO);

    @PostMapping(PREFIX + "/requireCreatingCodeInfo")
    CommonResult<CodeRecordGeneratorCodeInfoRespVO> requireCreatingCodeInfo(@Valid @RequestBody RequireCodeInfoCreateDTO createDTO);

    @GetMapping(PREFIX + "/createCodeByCodeContent")
    CommonResult<CodeRecordGeneratorCodeRespVO> createCodeByCodeContent(@RequestParam("codeContent") String codeContent,
                                                                        @RequestParam("sceneCodeUrl") String sceneCodeUrl);

}
