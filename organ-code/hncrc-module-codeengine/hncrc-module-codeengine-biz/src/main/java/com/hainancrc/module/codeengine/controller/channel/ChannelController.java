package com.hainancrc.module.codeengine.controller.channel;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.annotations.*;

import javax.validation.*;
import java.util.*;
import com.hainancrc.framework.common.pojo.PageResult;
import com.hainancrc.framework.common.pojo.CommonResult;
import static com.hainancrc.framework.common.pojo.CommonResult.success;


import com.hainancrc.module.codeengine.api.channel.dto.*;
import com.hainancrc.module.codeengine.api.channel.vo.*;
import com.hainancrc.module.codeengine.api.common.IdNameVO;
import com.hainancrc.module.codeengine.entity.ChannelDO;
import com.hainancrc.module.codeengine.convert.channel.ChannelConvert;
import com.hainancrc.module.codeengine.service.channel.ChannelService;

@Api(tags = "渠道")
@RestController
@RequestMapping("/codeengine/channel")
@Validated
public class ChannelController {

    @Resource
    private ChannelService channelService;

    @GetMapping("/keyValue")
    @ApiOperation("以 key-value 方式获取渠道内容")
    public CommonResult<List<IdNameVO>> getKeyValue() {
        return success(channelService.getKeyValue());
    }

    @PostMapping("/create")
    @ApiOperation("创建渠道")
    public CommonResult<Long> createChannel(@Valid @RequestBody ChannelCreateDTO createDTO) {
        return success(channelService.createChannel(createDTO));
    }

    @PutMapping("/update")
    @ApiOperation("更新渠道")
    public CommonResult<Boolean> updateChannel(@Valid @RequestBody ChannelUpdateDTO updateDTO) {
        channelService.updateChannel(updateDTO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除渠道")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Integer.class)
    public CommonResult<Boolean> deleteChannel(@RequestParam("id") Integer id) {
        channelService.deleteChannel(id);
        return success(true);
    }

    @GetMapping("/get")
    @ApiOperation("获得渠道")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Integer.class)
    public CommonResult<ChannelRespVO> getChannel(@RequestParam("id") Integer id) {
        ChannelDO channel = channelService.getChannel(id);
        return success(ChannelConvert.INSTANCE.convert(channel));
    }

    @GetMapping("/list")
    @ApiOperation("获得渠道列表")
    @ApiImplicitParam(name = "ids", value = "编号列表", required = true, example = "1024,2048", dataTypeClass = List.class)
    public CommonResult<List<ChannelRespVO>> getChannelList(@RequestParam("ids") Collection<Integer> ids) {
        List<ChannelDO> list = channelService.getChannelList(ids);
        return success(ChannelConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @ApiOperation("获得渠道分页")
    public CommonResult<PageResult<ChannelRespVO>> getChannelPage(@Valid ChannelPageDTO pageDTO) {
        PageResult<ChannelDO> pageResult = channelService.getChannelPage(pageDTO);
        return success(ChannelConvert.INSTANCE.convertPage(pageResult));
    }



}
