package com.hainancrc.organgateway.util;

import java.nio.charset.StandardCharsets;

import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;

import com.alibaba.fastjson.JSONObject;
import com.hainancrc.framework.common.exception.ErrorCode;
import com.hainancrc.framework.common.pojo.CommonResult;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public class ResultUtils {

    public static Mono resultFailJSON(ServerHttpResponse response, ErrorCode errorCode) {
        byte[] bytes = resultFailJSON(errorCode).getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = response.bufferFactory().wrap(bytes);
        response.setStatusCode(HttpStatus.OK);
        response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);

        return response.writeWith(Flux.just(buffer));
    }

    private static String resultFailJSON(ErrorCode errorCode) {
        CommonResult resultVo = CommonResult.error(errorCode);
        return JSONObject.toJSONString(resultVo);
    }

}
