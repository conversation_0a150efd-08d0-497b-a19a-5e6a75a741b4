package com.hainancrc.module.codeengine.api.applicationchannelrel.dto;

import lombok.*;
import io.swagger.annotations.*;
import javax.validation.constraints.*;

@ApiModel("应用渠道多对多更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApplicationChannelRelUpdateDTO extends ApplicationChannelRelBaseDTO {

    @ApiModelProperty(value = "主键ID", required = true)
    @NotNull(message = "主键ID不能为空")
    private Integer id;

}
