import { createRouterFlatModule, createRouterModule } from "@/router/util";

/** 智慧食堂运营端路由前缀 */
export const moduleName = "operations";

/** 运营端路由配置 */
export default createRouterModule({
  name: moduleName,
  options: {
    meta: {
      menu: false,
    },
  },
  children: [
    {
      path: "",
      meta: {
        title: "首页",
      },
      redirect: "home",
    },
    {
      path: "index",
      meta: {
        title: "首页",
      },
      redirect: "home",
    },
    {
      path: "home",
      meta: {
        title: "就餐码使用",
      },
      component: () => import("@/views/canteen/operations/home/<USER>"),
    },
    {
      path: "scanQrCode",
      meta: {
        title: "扫码就餐",
      },
      component: () =>
        import("@/views/canteen/operations/scanQrCode/index.vue"),
    },

    ...createRouterFlatModule({
      name: "scanQrCode",
      children: [
        {
          path: "result",
          component: () =>
            import("@/views/canteen/operations/scanQrCode/scanResult.vue"),
          meta: {
            title: "扫码结果",
          },
        },
      ],
    }),
  ],
});
