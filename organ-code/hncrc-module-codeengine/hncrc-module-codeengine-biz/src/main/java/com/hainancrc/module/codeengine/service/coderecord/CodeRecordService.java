package com.hainancrc.module.codeengine.service.coderecord;

import com.hainancrc.module.codeengine.api.coderecord.CodeRecordCreateDTO;
import com.hainancrc.module.codeengine.api.coderecord.RequireCodeInfoCreateDTO;
import com.hainancrc.module.codeengine.api.coderecord.vo.BatchCodeRecordGeneratorCodeRespVO;
import com.hainancrc.module.codeengine.api.coderecord.vo.CodeRecordGeneratorCodeInfoRespVO;
import com.hainancrc.module.codeengine.api.coderecord.vo.CodeRecordGeneratorCodeRespVO;

import javax.validation.Valid;

/**
 * 数据源 Service 接口
 *
 * <AUTHOR>
 */
public interface CodeRecordService {

    /**
     * 生成码
     */
    CodeRecordGeneratorCodeRespVO createCodeImage(CodeRecordCreateDTO createDTO);

    /**
     * 生成码批量导入
     */
    BatchCodeRecordGeneratorCodeRespVO createCodeBatch(Long applicationId, byte[] fileBytes);

    /**
     * 根据订单号获取之前生成的码
     */
    byte[] getCodesByGenerateOrderId(String generateOrderId);

    String getDownloadNameFromGenerateOrderId(String generateOrderId);


    CodeRecordGeneratorCodeInfoRespVO requireCreatingCodeInfo(@Valid RequireCodeInfoCreateDTO createDTO);

    CodeRecordGeneratorCodeRespVO createCodeImage(String codeContent, String sceneCodeUrl);
}
