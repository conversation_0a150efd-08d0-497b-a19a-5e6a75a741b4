package  com.hainancrc.module.codeengine.api.scene.dto;

import lombok.*;
import java.util.*;
import io.swagger.annotations.*;


@ApiModel(value = "场景 Excel 导出 Request VO", description = "参数和 ScenePageReqVO 是一致的")
@Data
public class SceneExportListDTO {

    @ApiModelProperty(value = "场景code")
    private String sceneCode;
    @ApiModelProperty(value = "场景名称")
    private String sceneName;
    @ApiModelProperty(value = "场景描述")
    private String sceneDescription;
    @ApiModelProperty(value = "申码授权ID")
    private Integer applyAuthId;
    @ApiModelProperty(value = "生码授权ID")
    private Integer generateAuthId;
    @ApiModelProperty(value = "亮码授权ID")
    private Integer showAuthId;
    @ApiModelProperty(value = "静态规则过期月数(null为永久有效)")
    private Integer staticExpiredMonth;
    @ApiModelProperty(value = "动态规则过期次数")
    private Integer dynamicExpiredTimes;
    @ApiModelProperty(value = "动态规则过期分钟数")
    private Integer dynamicExpiredMinute;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
