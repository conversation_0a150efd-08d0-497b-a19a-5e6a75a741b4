package com.hainancrc.module.codeengine.entity;

import lombok.*;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import com.hainancrc.framework.mybatis.core.dataobject.BaseDO;
import com.hainancrc.module.codeengine.enums.ApplyCategory;

/**
 * 申码记录 DO
 *
 * <AUTHOR>
 */
@TableName("codeengine_apply")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplyDO extends BaseDO {

    /**
     * 申码记录ID
     */
    @TableId
    private Long id;
    /**
     * 生成数量
     */
    private Integer generatedCount;
    /**
     * 应用ID
     */
    private Long applicationId;
    /**
     * 场景ID
     */
    private Long sceneId;
    /**
     * 主体 id
     */
    private String entityId;
    /**
     * 主体
     */
    private String entity;
    /**
     * 码过期时间
     */
    private Date expiredTime;

    /**
     * 申码描述
     */
    private String applyDescription;

}
