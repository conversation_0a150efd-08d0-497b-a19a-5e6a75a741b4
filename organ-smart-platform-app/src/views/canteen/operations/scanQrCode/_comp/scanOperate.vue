<template>
  <div class="scan-operate">
    <div class="content">
      <!-- 扫码提示 -->
      <div class="scan-tips">
        <img class="qrcode-img" src="@/assets/images/canteen/qrcode.png" />
        <div class="tips">点击开始扫码</div>
      </div>
    </div>
    <div class="actions-btns">
      <div
        class="canteen-btn btn-primary action-btn"
        @click.stop="handleScanQRCode"
      >
        开始扫码
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import router from "@/router";
import { BIZ_MODULE_ROUTE_PREFIX } from "@/views/canteen/configs/constants";

/**
 * 处理开始扫码按钮点击事件
 */
const handleScanQRCode = () => {
  // TODO: 实现扫码逻辑
  console.log("唤起海政通扫码");
  // TODO: dev测试数据
  router.replace({
    path: `${BIZ_MODULE_ROUTE_PREFIX}/operations/scanQrCode/result`,
    query: {
      bookingCode: "123456",
    },
  });
};
</script>

<style scoped lang="scss">
.scan-operate {
  background: #ffffff;
  border-radius: 10px;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;

  .content {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    margin: 0 auto;
  }

  .scan-tips {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    color: #808080;
    border: 2px dashed #e0e0e0;
    border-radius: 8px;
    padding: 30px 46px;
    // font-size: 16px;

    .tips {
      font-size: 16px;
      color: #808080;
      line-height: 1;
    }

    .qrcode-img {
      width: auto;
      height: auto;
      max-width: 160px;
      max-height: 160px;

      margin-bottom: 20px;
    }
  }

  .actions-btns {
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 12px;

    .action-btn {
      flex: 1;
      max-width: 86%;
      padding: 16px;
      font-size: 18px;
    }
  }
}
</style>
