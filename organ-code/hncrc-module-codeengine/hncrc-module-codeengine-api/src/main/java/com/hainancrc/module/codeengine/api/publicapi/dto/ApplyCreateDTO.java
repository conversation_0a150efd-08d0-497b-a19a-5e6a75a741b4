package  com.hainancrc.module.codeengine.api.publicapi.dto;

import lombok.*;
import io.swagger.annotations.*;

import java.util.Date;

import javax.validation.constraints.*;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hainancrc.module.codeengine.enums.ApplyCategory;

@ApiModel("申码记录创建 Request VO")
@Data
public class ApplyCreateDTO {

    @ApiModelProperty(value = "应用Code", required = true)
    @NotNull(message = "应用Code不能为空")
    private String application;

    @ApiModelProperty(value = "渠道Code", required = true)
    @NotNull(message = "渠道Code不能为空")
    private String channel;

    @ApiModelProperty(value = "场景Code", required = true)
    @NotNull(message = "场景Code不能为空")
    private String scene;

    @ApiModelProperty(value = "主体id", required = true)
    private String entityId;

    @ApiModelProperty(value = "主体", required = true)
    private String entity;
    
    @ApiModelProperty(value="码有效期")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expiredTime;

    @ApiModelProperty(value = "申码分类(dynamic/static)", required = true)
    @NotNull(message = "申码分类(dynamic/static)不能为空")
    private ApplyCategory category;

    @ApiModelProperty(value = "申码描述",required = true)
    private String description;

}
